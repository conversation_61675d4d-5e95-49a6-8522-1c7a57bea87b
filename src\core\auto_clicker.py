#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动点击核心模块
实现在指定区域内的自动鼠标点击功能
"""

import pyautogui
import random
import time
import threading
from typing import Tuple, Optional, Callable
import win32gui
import win32con
from ..utils.safety_checker import <PERSON><PERSON><PERSON><PERSON>
from ..utils.game_response_detector import <PERSON>ResponseDetector
from ..utils.game_window_manager import GameWindowManager
from ..utils.captcha_detector import CaptchaDetector
from ..utils.logger import get_logger
from .enhanced_mouse import EnhancedMouse, ClickMode

class AutoClicker:
    """自动点击器"""
    
    def __init__(self):
        self.is_running = False
        self.click_thread = None
        self.target_region = None  # (left, top, right, bottom)
        self.target_hwnd = None
        self.click_interval = (0.5, 2.0)  # 点击间隔范围（秒）- 缩短间隔提高响应
        self.status_callback = None
        self.safety_checker = SafetyChecker()
        self.enhanced_mouse = EnhancedMouse()
        self.response_detector = GameResponseDetector()
        self.window_manager = GameWindowManager()
        self.captcha_detector = CaptchaDetector()
        self.logger = get_logger()

        # 设置pyautogui安全设置
        pyautogui.FAILSAFE = True  # 鼠标移到左上角停止
        pyautogui.PAUSE = 0.1  # 每次操作后暂停

        # 设置回调函数
        self.safety_checker.set_safety_callback(self._log_status)
        self.enhanced_mouse.set_status_callback(self._log_status)
        self.response_detector.set_status_callback(self._log_status)
        self.captcha_detector.set_status_callback(self._log_status)
        self.captcha_detector.set_captcha_detected_callback(self._on_captcha_detected)

    def set_click_interval(self, min_interval: float, max_interval: float):
        """设置点击间隔范围"""
        self.click_interval = (min_interval, max_interval)
        self._log_status(f"点击间隔已设置为: {min_interval}-{max_interval}秒")

    def set_click_mode(self, mode: str):
        """设置点击模式"""
        from .enhanced_mouse import ClickMode

        mode_map = {
            "normal": ClickMode.NORMAL,
            "continuous": ClickMode.CONTINUOUS,
            "hold": ClickMode.HOLD,
            "smooth": ClickMode.SMOOTH
        }

        if mode.lower() in mode_map:
            click_mode = mode_map[mode.lower()]
            self.enhanced_mouse.set_click_mode(click_mode)
            self._log_status(f"点击模式已设置为: {mode}")
        else:
            self._log_status(f"未知的点击模式: {mode}")

    def enable_response_detection(self, enabled: bool = True):
        """启用/禁用响应检测"""
        self.response_detector.detection_enabled = enabled
        status = "启用" if enabled else "禁用"
        self._log_status(f"游戏响应检测已{status}")

    def set_captcha_template(self, template_path: str):
        """设置验证码模板图片"""
        self.captcha_detector.set_captcha_template(template_path)

    def enable_captcha_detection(self, enabled: bool = True):
        """启用/禁用验证码检测"""
        if enabled:
            if self.target_hwnd:
                self.captcha_detector.set_target_window(self.target_hwnd)
                if self.captcha_detector.start_monitoring():
                    self._log_status("验证码检测已启用")
                else:
                    self._log_status("验证码检测启用失败")
            else:
                self._log_status("未设置目标窗口，无法启用验证码检测")
        else:
            self.captcha_detector.stop_monitoring()
            self._log_status("验证码检测已禁用")

    def _on_captcha_detected(self):
        """验证码检测到时的回调处理"""
        self._log_status("🚨 检测到验证码，暂停自动点击")

        # 暂停点击
        was_running = self.is_running
        if was_running:
            self.stop_clicking()

        # 触发安全停止
        self.safety_checker.emergency_stop = True

        # 记录事件
        self.logger.log_emergency_stop("验证码检测", True)

        # 可以在这里添加更多处理逻辑，比如：
        # - 发送通知
        # - 播放提示音
        # - 保存当前状态等
        
    def set_target_region(self, region: Tuple[int, int, int, int], hwnd: Optional[int] = None):
        """
        设置目标点击区域
        region: (left, top, right, bottom)
        hwnd: 目标窗口句柄（可选）
        """
        self.target_region = region
        self.target_hwnd = hwnd

        # 设置窗口管理器的目标窗口
        if hwnd:
            self.window_manager.set_target_window(hwnd)
            # 同时设置验证码检测器的目标窗口
            self.captcha_detector.set_target_window(hwnd)

            # 自动启用验证码检测（如果配置中启用了）
            from ..config.settings import Settings
            settings = Settings()
            captcha_settings = settings.get_captcha_settings()

            if captcha_settings.get('captcha_detection', False):
                template_path = captcha_settings.get('captcha_template', 'templates/captcha_default.png')
                self.set_captcha_template(template_path)
                self.enable_captcha_detection(True)
                self._log_status(f"✅ 自动启用验证码检测: {template_path}")
        
    def set_click_interval(self, min_interval: float, max_interval: float):
        """设置点击间隔范围"""
        self.click_interval = (min_interval, max_interval)

    def set_status_callback(self, callback: Callable[[str], None]):
        """设置状态回调函数"""
        self.status_callback = callback

    def set_click_mode(self, mode: ClickMode):
        """设置点击模式"""
        self.enhanced_mouse.set_click_mode(mode)
        self._log_status(f"点击模式已设置为: {mode.value}")

    def configure_mouse_behavior(self, mode_config: dict):
        """配置鼠标行为参数"""
        if 'click_behavior' in mode_config:
            self.enhanced_mouse.configure_click_behavior(**mode_config['click_behavior'])
        if 'continuous_mode' in mode_config:
            self.enhanced_mouse.configure_continuous_mode(**mode_config['continuous_mode'])
        if 'hold_mode' in mode_config:
            self.enhanced_mouse.configure_hold_mode(**mode_config['hold_mode'])
        if 'smooth_mode' in mode_config:
            self.enhanced_mouse.configure_smooth_mode(**mode_config['smooth_mode'])

    def enable_response_detection(self, enabled: bool = True):
        """启用/禁用游戏响应检测"""
        self.response_detector.enable_detection(enabled)

    def configure_response_detection(self, **kwargs):
        """配置响应检测参数"""
        self.response_detector.configure_detection(**kwargs)
        
    def _log_status(self, message: str):
        """记录状态信息"""
        if self.status_callback:
            self.status_callback(message)
        print(f"[AutoClicker] {message}")
        
    def start_clicking(self) -> bool:
        """开始自动点击"""
        if self.is_running:
            self._log_status("自动点击已在运行中")
            return False

        if not self.target_region:
            self._log_status("错误：未设置目标区域")
            return False

        # 重置安全检查器状态（包括ESC键状态）
        self.safety_checker.reset_emergency_stop()
        self._log_status("🔄 安全检查器状态已重置，ESC键可重新使用")

        # 启动安全监控
        self.safety_checker.start_monitoring()

        # 启动响应检测监控
        if self.response_detector.detection_enabled:
            self.response_detector.start_monitoring(self.target_hwnd, self.target_region)

        self.is_running = True
        self.click_thread = threading.Thread(target=self._click_loop, daemon=True)
        self.click_thread.start()
        self._log_status("开始自动点击")
        return True
        
    def stop_clicking(self):
        """停止自动点击"""
        if self.is_running:
            self._log_status("正在停止自动点击...")
            self.is_running = False

            # 等待点击线程结束
            if self.click_thread and self.click_thread.is_alive():
                self.click_thread.join(timeout=3.0)

                if self.click_thread.is_alive():
                    self._log_status("警告：点击线程未能正常结束")
                    self.logger.log_thread_state("ClickThread", "强制结束", "线程未能在超时时间内结束")
                else:
                    self.logger.log_thread_state("ClickThread", "正常结束", "点击线程已正常结束")

            # 停止安全监控
            self.safety_checker.stop_monitoring()

            # 停止响应检测监控
            self.response_detector.stop_monitoring()

            self._log_status("自动点击已停止")
        
    def _click_loop(self):
        """点击循环主函数"""
        click_count = 0
        self._log_status("🚀 开始自动点击循环")

        while self.is_running:
            try:
                click_count += 1
                loop_start_time = time.time()

                self._log_status(f"📍 第 {click_count} 次点击循环开始")

                # 检查安全状态
                if self.safety_checker.emergency_stop:
                    self._log_status("🛑 检测到紧急停止信号，停止点击")
                    break

                # 检查验证码（每5次点击检查一次，避免过于频繁）
                if click_count % 5 == 0:
                    detected, x, y = self.captcha_detector.detect_captcha()
                    if detected:
                        self._log_status("🚨 检测到验证码弹窗，立即停止点击")
                        self._on_captcha_detected()
                        break

                # 检查目标窗口是否仍然有效
                if self.target_hwnd and not self._is_window_valid():
                    self._log_status("❌ 目标窗口已关闭，停止点击")
                    break

                # 生成随机点击位置
                position_start_time = time.time()
                click_x, click_y = self._generate_random_position()
                position_time = time.time() - position_start_time
                self._log_status(f"🎯 生成点击位置: ({click_x}, {click_y}) 耗时: {position_time:.3f}秒")

                # 执行点击
                click_start_time = time.time()
                click_success = self._perform_click(click_x, click_y)
                click_time = time.time() - click_start_time

                if click_success:
                    self._log_status(f"✅ 点击成功: ({click_x}, {click_y}) 耗时: {click_time:.3f}秒")

                    # 简化响应检测，避免长时间阻塞
                    if self.response_detector.detection_enabled:
                        # 设置较短的响应检测超时
                        detection_start_time = time.time()
                        try:
                            # 使用较短的超时时间
                            has_response = self.response_detector.detect_response_after_click(
                                click_x, click_y, timeout=1.0  # 1秒超时
                            )
                            detection_time = time.time() - detection_start_time

                            if has_response:
                                self._log_status(f"🎮 检测到游戏响应 耗时: {detection_time:.3f}秒")
                            else:
                                self._log_status(f"⚠️ 未检测到游戏响应 耗时: {detection_time:.3f}秒")

                        except Exception as e:
                            self._log_status(f"⚠️ 响应检测异常: {e}")
                else:
                    self._log_status(f"❌ 点击失败: ({click_x}, {click_y}) 耗时: {click_time:.3f}秒")

                # 计算等待时间
                wait_time = random.uniform(*self.click_interval)
                loop_total_time = time.time() - loop_start_time

                self._log_status(f"⏱️ 第 {click_count} 次循环完成，总耗时: {loop_total_time:.3f}秒，等待: {wait_time:.3f}秒")

                # 等待下次点击
                time.sleep(wait_time)

            except Exception as e:
                self._log_status(f"💥 点击过程中发生错误: {e}")
                import traceback
                self._log_status(f"错误详情: {traceback.format_exc()}")
                time.sleep(1.0)

        self.is_running = False
        self._log_status(f"🏁 点击循环结束，总共执行了 {click_count} 次点击")
        
    def _generate_random_position(self) -> Tuple[int, int]:
        """生成区域内的随机点击位置"""
        # 传递用户选择的区域给窗口管理器
        safe_area = self.window_manager.get_safe_click_area(self.target_region)

        if safe_area:
            left, top, right, bottom = safe_area
        else:
            # 回退到原始区域
            left, top, right, bottom = self.target_region

        # 确保区域有效
        if left >= right:
            left, right = self.target_region[0], self.target_region[2]
        if top >= bottom:
            top, bottom = self.target_region[1], self.target_region[3]

        # 生成更大范围的随机位置，增加移动距离
        x = random.randint(left, right)
        y = random.randint(top, bottom)

        # 记录生成的坐标用于调试
        self.logger.main_logger.debug(f"生成随机位置: ({x}, {y}) 在区域 {safe_area}")

        return x, y
        
    def _perform_click(self, x: int, y: int) -> bool:
        """执行鼠标点击"""
        try:
            # 安全检查
            if not self.safety_checker.is_safe_to_click(x, y, self.target_region, self.target_hwnd):
                return False

            # 检查坐标是否在游戏可操作区域（传递用户区域）
            if not self.window_manager.is_coordinate_in_game_area(x, y, self.target_region):
                # 只在连续失败时记录，避免日志过多
                if not hasattr(self, '_consecutive_area_fails'):
                    self._consecutive_area_fails = 0
                self._consecutive_area_fails += 1

                if self._consecutive_area_fails <= 3 or self._consecutive_area_fails % 10 == 0:
                    self._log_status(f"坐标 ({x}, {y}) 不在用户选择区域 {self.target_region} 内 (第{self._consecutive_area_fails}次)")
                return False
            else:
                # 重置失败计数
                if hasattr(self, '_consecutive_area_fails'):
                    self._consecutive_area_fails = 0

            # 为点击做准备（确保窗口焦点等）
            if not self.window_manager.prepare_for_click():
                self._log_status("点击准备失败，跳过本次点击")
                return False

            # 使用增强的鼠标操作
            success = self.enhanced_mouse.enhanced_click(x, y)

            if success:
                self.logger.log_mouse_operation("游戏点击", x, y, True)
            else:
                self.logger.log_mouse_operation("游戏点击", x, y, False, "增强鼠标操作失败")

            return success

        except Exception as e:
            self._log_status(f"点击执行失败: {e}")
            self.logger.log_mouse_operation("游戏点击", x, y, False, str(e))
            return False
            
    def _is_position_in_region(self, x: int, y: int) -> bool:
        """检查位置是否在目标区域内"""
        if not self.target_region:
            return False
            
        left, top, right, bottom = self.target_region
        return left <= x <= right and top <= y <= bottom
        
    def _is_window_valid(self) -> bool:
        """检查目标窗口是否仍然有效"""
        if not self.target_hwnd:
            return True
            
        try:
            return win32gui.IsWindow(self.target_hwnd) and win32gui.IsWindowVisible(self.target_hwnd)
        except:
            return False
            
    def get_status(self) -> dict:
        """获取当前状态信息"""
        status = {
            'is_running': self.is_running,
            'target_region': self.target_region,
            'target_hwnd': self.target_hwnd,
            'click_interval': self.click_interval,
            'mouse_config': self.enhanced_mouse.get_current_config(),
            'response_stats': self.response_detector.get_response_statistics()
        }
        return status

    def recover_from_emergency_stop(self) -> bool:
        """从紧急停止状态恢复"""
        try:
            self._log_status("正在从紧急停止状态恢复...")

            # 停止当前所有操作
            if self.is_running:
                self.stop_clicking()

            # 强制重置安全检查器
            self.safety_checker.force_reset()

            # 重置响应检测器
            self.response_detector.stop_monitoring()
            self.response_detector.reset_statistics()

            # 重置增强鼠标状态
            if hasattr(self.enhanced_mouse, '_win32_fail_count'):
                delattr(self.enhanced_mouse, '_win32_fail_count')

            # 重新检查API可用性
            self.enhanced_mouse._check_api_availability()

            self.logger.log_emergency_stop("恢复操作", True)
            self._log_status("紧急停止恢复完成，可以重新启动")
            return True

        except Exception as e:
            self._log_status(f"紧急停止恢复失败: {e}")
            self.logger.log_emergency_stop("恢复操作", False)
            return False

    def is_emergency_stopped(self) -> bool:
        """检查是否处于紧急停止状态"""
        return self.safety_checker.emergency_stop
