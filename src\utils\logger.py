#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面错误日志系统
用于详细记录程序运行状态和错误信息
"""

import logging
import os
import sys
import time
import threading
import platform
import ctypes
from datetime import datetime
from typing import Dict, Any, Optional
import win32api
import win32con
import win32security

class AutoWalkLogger:
    """自动走路工具专用日志记录器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = log_dir
        self.ensure_log_directory()
        
        # 创建不同类型的日志记录器
        self.main_logger = self._create_logger("main", "auto_walk_main.log")
        self.mouse_logger = self._create_logger("mouse", "mouse_operations.log")
        self.error_logger = self._create_logger("error", "auto_walk_error.log")
        self.system_logger = self._create_logger("system", "system_info.log")
        self.api_logger = self._create_logger("api", "win32_api_calls.log")
        
        # 记录系统信息
        self.log_system_info()
        
    def ensure_log_directory(self):
        """确保日志目录存在"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
            
    def _create_logger(self, name: str, filename: str) -> logging.Logger:
        """创建专用日志记录器"""
        logger = logging.getLogger(f"autowalk_{name}")
        logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
            
        # 文件处理器
        file_handler = logging.FileHandler(
            os.path.join(self.log_dir, filename), 
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
        
    def log_system_info(self):
        """记录系统信息"""
        try:
            info = {
                "操作系统": platform.system(),
                "系统版本": platform.version(),
                "处理器": platform.processor(),
                "Python版本": sys.version,
                "是否管理员": self.is_admin(),
                "UAC状态": self.get_uac_status(),
                "用户权限": self.get_user_privileges(),
                "时间戳": datetime.now().isoformat()
            }
            
            self.system_logger.info("=== 系统信息 ===")
            for key, value in info.items():
                self.system_logger.info(f"{key}: {value}")
                
        except Exception as e:
            self.error_logger.error(f"记录系统信息失败: {e}")
            
    def is_admin(self) -> bool:
        """检查是否以管理员权限运行"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
            
    def get_uac_status(self) -> str:
        """获取UAC状态"""
        try:
            import winreg
            key = winreg.OpenKey(
                winreg.HKEY_LOCAL_MACHINE,
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System"
            )
            value, _ = winreg.QueryValueEx(key, "EnableLUA")
            winreg.CloseKey(key)
            return "启用" if value else "禁用"
        except:
            return "未知"
            
    def get_user_privileges(self) -> Dict[str, Any]:
        """获取用户权限信息"""
        try:
            token = win32security.OpenProcessToken(
                win32api.GetCurrentProcess(),
                win32con.TOKEN_QUERY
            )
            
            # 获取权限信息
            privileges = win32security.GetTokenInformation(
                token, win32security.TokenPrivileges
            )
            
            privilege_names = []
            for privilege in privileges:
                try:
                    name = win32security.LookupPrivilegeName(None, privilege[0])
                    privilege_names.append(name)
                except:
                    continue
                    
            return {
                "权限数量": len(privilege_names),
                "主要权限": privilege_names[:10]  # 只显示前10个
            }
        except Exception as e:
            return {"错误": str(e)}
            
    def log_mouse_operation(self, operation: str, x: int, y: int, 
                          result: bool, error: Optional[str] = None):
        """记录鼠标操作"""
        status = "成功" if result else "失败"
        message = f"鼠标操作: {operation} 位置:({x}, {y}) 结果:{status}"
        
        if error:
            message += f" 错误:{error}"
            
        if result:
            self.mouse_logger.info(message)
        else:
            self.mouse_logger.error(message)
            self.error_logger.error(message)
            
    def log_win32_api_call(self, api_name: str, params: Dict[str, Any], 
                          result: Any, error_code: Optional[int] = None):
        """记录Win32 API调用"""
        message = f"API调用: {api_name} 参数:{params} 结果:{result}"
        
        if error_code:
            try:
                error_msg = win32api.FormatMessage(error_code)
                message += f" 错误码:{error_code} 错误信息:{error_msg.strip()}"
            except:
                message += f" 错误码:{error_code}"
                
        if error_code:
            self.api_logger.error(message)
            self.error_logger.error(message)
        else:
            self.api_logger.info(message)
            
    def log_thread_state(self, thread_name: str, state: str, details: str = ""):
        """记录线程状态"""
        message = f"线程状态: {thread_name} -> {state}"
        if details:
            message += f" 详情:{details}"
            
        self.main_logger.info(message)
        
    def log_emergency_stop(self, trigger: str, cleanup_success: bool):
        """记录紧急停止事件"""
        message = f"紧急停止触发: {trigger} 清理{'成功' if cleanup_success else '失败'}"
        
        if cleanup_success:
            self.main_logger.warning(message)
        else:
            self.error_logger.error(message)
            
    def log_click_mode_change(self, old_mode: str, new_mode: str, success: bool):
        """记录点击模式变更"""
        message = f"点击模式变更: {old_mode} -> {new_mode} {'成功' if success else '失败'}"
        
        if success:
            self.main_logger.info(message)
        else:
            self.error_logger.error(message)
            
    def analyze_error_patterns(self) -> Dict[str, Any]:
        """分析错误模式"""
        try:
            error_log_path = os.path.join(self.log_dir, "auto_walk_error.log")
            
            if not os.path.exists(error_log_path):
                return {"状态": "无错误日志文件"}
                
            with open(error_log_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 统计错误类型
            error_types = {}
            api_errors = {}
            mouse_errors = 0
            
            for line in lines:
                if "SetCursorPos" in line:
                    api_errors["SetCursorPos"] = api_errors.get("SetCursorPos", 0) + 1
                if "鼠标操作" in line and "失败" in line:
                    mouse_errors += 1
                if "紧急停止" in line:
                    error_types["紧急停止"] = error_types.get("紧急停止", 0) + 1
                    
            return {
                "总错误数": len(lines),
                "鼠标操作错误": mouse_errors,
                "API调用错误": api_errors,
                "其他错误类型": error_types,
                "分析时间": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"分析失败": str(e)}
            
    def get_log_summary(self) -> Dict[str, Any]:
        """获取日志摘要"""
        summary = {}
        
        for log_name, filename in [
            ("主日志", "auto_walk_main.log"),
            ("鼠标操作", "mouse_operations.log"),
            ("错误日志", "auto_walk_error.log"),
            ("系统信息", "system_info.log"),
            ("API调用", "win32_api_calls.log")
        ]:
            log_path = os.path.join(self.log_dir, filename)
            if os.path.exists(log_path):
                stat = os.stat(log_path)
                summary[log_name] = {
                    "文件大小": f"{stat.st_size} 字节",
                    "修改时间": datetime.fromtimestamp(stat.st_mtime).isoformat()
                }
            else:
                summary[log_name] = "文件不存在"
                
        return summary

# 全局日志实例
_logger_instance = None

def get_logger() -> AutoWalkLogger:
    """获取全局日志实例"""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = AutoWalkLogger()
    return _logger_instance
