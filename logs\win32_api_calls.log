2025-07-27 13:08:26 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (633, 217)} 结果:None
2025-07-27 13:08:26 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 13:18:47 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1181, 906)} 结果:None
2025-07-27 13:18:47 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 13:19:25 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (823, 137)} 结果:None
2025-07-27 13:19:25 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 13:19:55 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1268, 786)} 结果:None
2025-07-27 13:19:55 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 13:22:04 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (801, 382)} 结果:None
2025-07-27 13:22:04 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 13:27:20 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1244, 901)} 结果:None
2025-07-27 13:27:20 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 13:27:21 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1244, 901)} 结果:None
2025-07-27 13:27:21 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 13:27:55 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (694, 157)} 结果:None
2025-07-27 13:27:55 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 13:40:24 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 13:40:24 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 13:41:09 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (639, 137)} 结果:None
2025-07-27 13:41:09 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 13:43:15 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1166, 390)} 结果:None
2025-07-27 13:43:15 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 13:44:29 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1555, 793)} 结果:None
2025-07-27 13:44:29 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 13:58:43 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (871, 216)} 结果:None
2025-07-27 13:58:43 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 14:20:18 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (835, 87)} 结果:None
2025-07-27 14:20:18 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 14:24:33 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1772, 945)} 结果:None
2025-07-27 14:24:33 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 14:29:04 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (902, 303)} 结果:None
2025-07-27 14:29:04 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 14:55:32 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1663, 943)} 结果:None
2025-07-27 14:55:32 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 15:24:05 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1479, 786)} 结果:None
2025-07-27 15:24:05 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 16:05:43 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1676, 1107)} 结果:None
2025-07-27 16:05:43 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 16:32:17 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (898, 445)} 结果:None
2025-07-27 16:32:17 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 16:43:19 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1220, 826)} 结果:None
2025-07-27 16:43:19 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 16:44:06 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1160, 832)} 结果:None
2025-07-27 16:44:06 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 16:44:35 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1235, 838)} 结果:None
2025-07-27 16:44:35 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 19:25:34 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1770, 214)} 结果:None
2025-07-27 19:25:34 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 21:50:14 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (823, 430)} 结果:None
2025-07-27 21:50:14 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 21:56:12 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1167, 824)} 结果:None
2025-07-27 21:56:12 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 21:57:51 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (1055, 354)} 结果:None
2025-07-27 21:57:51 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
2025-07-27 22:26:45 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'position': (896, 371)} 结果:None
2025-07-27 22:26:45 - autowalk_api - INFO - API调用: SetCursorPos 参数:{'test': True} 结果:False
