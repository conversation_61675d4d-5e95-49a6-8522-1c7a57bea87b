#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知系统
支持多种通知方式的统一通知系统
"""

import time
import threading
import winsound
from typing import Dict, Any, Callable, Optional
from enum import Enum
import tkinter as tk
from tkinter import messagebox
from ..utils.logger import get_logger


class NotificationType(Enum):
    """通知类型枚举"""
    SOUND = "sound"           # 声音通知
    POPUP = "popup"           # 弹窗通知
    LOG = "log"               # 日志通知
    CALLBACK = "callback"     # 回调通知


class NotificationSystem:
    """通知系统"""
    
    def __init__(self):
        self.logger = get_logger()
        self.enabled_types = {
            NotificationType.SOUND: True,
            NotificationType.POPUP: True,
            NotificationType.LOG: True,
            NotificationType.CALLBACK: True
        }
        
        # 冷却机制
        self.cooldown_periods = {}  # 每种通知类型的冷却期（秒）
        self.last_notification_times = {}  # 每种通知类型的最后通知时间
        
        # 回调函数
        self.notification_callbacks = {}
        
        # 声音设置
        self.sound_enabled = True
        self.sound_frequency = 1000  # 频率
        self.sound_duration = 500    # 持续时间（毫秒）
        
        # 弹窗设置
        self.popup_enabled = True
        self.popup_title = "系统通知"
        
        # 初始化默认冷却期
        self.set_default_cooldowns()
        
    def set_default_cooldowns(self):
        """设置默认冷却期"""
        self.cooldown_periods = {
            "captcha_detected": 300,      # CAPTCHA检测冷却5分钟
            "emergency_stop": 60,         # 紧急停止冷却1分钟
            "system_error": 120,          # 系统错误冷却2分钟
            "window_lost": 30,            # 窗口丢失冷却30秒
        }
        
    def set_cooldown_period(self, notification_key: str, seconds: int):
        """设置特定通知的冷却期"""
        self.cooldown_periods[notification_key] = seconds
        # 使用AutoWalkLogger的main_logger
        if hasattr(self.logger, 'main_logger'):
            self.logger.main_logger.info(f"通知冷却期设置: {notification_key} = {seconds}秒")
        else:
            print(f"通知冷却期设置: {notification_key} = {seconds}秒")
        
    def enable_notification_type(self, notification_type: NotificationType, enabled: bool = True):
        """启用/禁用特定类型的通知"""
        self.enabled_types[notification_type] = enabled
        status = "启用" if enabled else "禁用"
        # 使用AutoWalkLogger的main_logger
        if hasattr(self.logger, 'main_logger'):
            self.logger.main_logger.info(f"通知类型{notification_type.value}已{status}")
        else:
            print(f"通知类型{notification_type.value}已{status}")
        
    def set_notification_callback(self, callback_name: str, callback: Callable[[str, str], None]):
        """设置通知回调函数"""
        self.notification_callbacks[callback_name] = callback
        
    def _is_in_cooldown(self, notification_key: str) -> bool:
        """检查是否在冷却期内"""
        if notification_key not in self.cooldown_periods:
            return False
            
        cooldown_period = self.cooldown_periods[notification_key]
        last_time = self.last_notification_times.get(notification_key, 0)
        current_time = time.time()
        
        return (current_time - last_time) < cooldown_period
        
    def _update_last_notification_time(self, notification_key: str):
        """更新最后通知时间"""
        self.last_notification_times[notification_key] = time.time()
        
    def _play_sound(self, frequency: Optional[int] = None, duration: Optional[int] = None):
        """播放提示音"""
        if not self.sound_enabled or not self.enabled_types[NotificationType.SOUND]:
            return
            
        try:
            freq = frequency or self.sound_frequency
            dur = duration or self.sound_duration
            
            # 在新线程中播放声音，避免阻塞
            def play():
                try:
                    winsound.Beep(freq, dur)
                except Exception as e:
                    if hasattr(self.logger, 'error_logger'):
                        self.logger.error_logger.error(f"播放提示音失败: {e}")
                    else:
                        print(f"播放提示音失败: {e}")

            threading.Thread(target=play, daemon=True).start()

        except Exception as e:
            if hasattr(self.logger, 'error_logger'):
                self.logger.error_logger.error(f"播放提示音异常: {e}")
            else:
                print(f"播放提示音异常: {e}")
            
    def _show_popup(self, title: str, message: str, message_type: str = "warning"):
        """显示弹窗通知"""
        if not self.popup_enabled or not self.enabled_types[NotificationType.POPUP]:
            return
            
        try:
            # 在新线程中显示弹窗，避免阻塞
            def show():
                try:
                    if message_type == "error":
                        messagebox.showerror(title, message)
                    elif message_type == "info":
                        messagebox.showinfo(title, message)
                    else:
                        messagebox.showwarning(title, message)
                except Exception as e:
                    if hasattr(self.logger, 'error_logger'):
                        self.logger.error_logger.error(f"显示弹窗失败: {e}")
                    else:
                        print(f"显示弹窗失败: {e}")

            threading.Thread(target=show, daemon=True).start()

        except Exception as e:
            if hasattr(self.logger, 'error_logger'):
                self.logger.error_logger.error(f"显示弹窗异常: {e}")
            else:
                print(f"显示弹窗异常: {e}")
            
    def _log_notification(self, level: str, message: str):
        """记录日志通知"""
        if not self.enabled_types[NotificationType.LOG]:
            return

        try:
            if hasattr(self.logger, 'main_logger') and hasattr(self.logger, 'error_logger'):
                if level == "error":
                    self.logger.error_logger.error(message)
                elif level == "warning":
                    self.logger.main_logger.warning(message)
                elif level == "info":
                    self.logger.main_logger.info(message)
                else:
                    self.logger.main_logger.info(message)
            else:
                print(f"[{level.upper()}] {message}")
        except Exception as e:
            print(f"记录日志失败: {e}")
            
    def _call_callbacks(self, notification_key: str, message: str):
        """调用回调函数"""
        if not self.enabled_types[NotificationType.CALLBACK]:
            return
            
        for callback_name, callback in self.notification_callbacks.items():
            try:
                callback(notification_key, message)
            except Exception as e:
                if hasattr(self.logger, 'error_logger'):
                    self.logger.error_logger.error(f"回调函数{callback_name}执行失败: {e}")
                else:
                    print(f"回调函数{callback_name}执行失败: {e}")
                
    def send_notification(self, 
                         notification_key: str,
                         title: str,
                         message: str,
                         level: str = "warning",
                         enable_sound: bool = True,
                         enable_popup: bool = True,
                         enable_log: bool = True,
                         enable_callback: bool = True,
                         force: bool = False) -> bool:
        """
        发送通知
        
        Args:
            notification_key: 通知键（用于冷却控制）
            title: 通知标题
            message: 通知消息
            level: 日志级别 (info/warning/error)
            enable_sound: 是否启用声音
            enable_popup: 是否启用弹窗
            enable_log: 是否启用日志
            enable_callback: 是否启用回调
            force: 是否强制发送（忽略冷却期）
            
        Returns:
            bool: 是否成功发送通知
        """
        try:
            # 检查冷却期
            if not force and self._is_in_cooldown(notification_key):
                remaining = self.cooldown_periods.get(notification_key, 0) - (
                    time.time() - self.last_notification_times.get(notification_key, 0)
                )
                # 使用print代替debug，因为AutoWalkLogger没有debug方法
                print(f"通知{notification_key}在冷却期内，剩余{remaining:.0f}秒")
                return False
                
            # 更新最后通知时间
            self._update_last_notification_time(notification_key)
            
            # 发送各种类型的通知
            if enable_sound:
                self._play_sound()
                
            if enable_popup:
                self._show_popup(title, message, level)
                
            if enable_log:
                log_message = f"[{title}] {message}"
                self._log_notification(level, log_message)
                
            if enable_callback:
                self._call_callbacks(notification_key, message)

            if hasattr(self.logger, 'main_logger'):
                self.logger.main_logger.info(f"通知已发送: {notification_key} - {title}")
            else:
                print(f"通知已发送: {notification_key} - {title}")
            return True

        except Exception as e:
            if hasattr(self.logger, 'error_logger'):
                self.logger.error_logger.error(f"发送通知失败: {e}")
            else:
                print(f"发送通知失败: {e}")
            return False
            
    def send_captcha_alert(self, message: str = "检测到验证码弹窗！") -> bool:
        """发送CAPTCHA警报"""
        return self.send_notification(
            notification_key="captcha_detected",
            title="验证码检测",
            message=f"{message}\n\n自动点击已停止，请处理验证码后重新启动。",
            level="warning",
            enable_sound=True,
            enable_popup=True,
            enable_log=True,
            enable_callback=True
        )
        
    def send_emergency_stop_alert(self, reason: str) -> bool:
        """发送紧急停止警报"""
        return self.send_notification(
            notification_key="emergency_stop",
            title="紧急停止",
            message=f"系统触发紧急停止\n\n原因: {reason}",
            level="error",
            enable_sound=True,
            enable_popup=True,
            enable_log=True,
            enable_callback=True
        )
        
    def get_cooldown_status(self) -> Dict[str, Dict[str, Any]]:
        """获取冷却状态"""
        status = {}
        current_time = time.time()
        
        for key, cooldown_period in self.cooldown_periods.items():
            last_time = self.last_notification_times.get(key, 0)
            remaining = max(0, cooldown_period - (current_time - last_time))
            
            status[key] = {
                "cooldown_period": cooldown_period,
                "last_notification": last_time,
                "remaining_cooldown": remaining,
                "is_in_cooldown": remaining > 0
            }
            
        return status
