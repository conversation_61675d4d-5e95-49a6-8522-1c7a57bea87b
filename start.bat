@echo off
echo 正在启动梦幻西游自动走路工具...
echo.

REM 尝试使用不同的Python命令
if exist "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python3.exe" (
    echo 使用 python3.exe 启动...
    "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python3.exe" main.py
) else if exist "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe" (
    echo 使用 python.exe 启动...
    "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe" main.py
) else (
    echo 尝试使用系统 python 命令...
    python main.py
)

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo 程序启动失败！
    echo 请确保已安装Python和所有依赖库。
    echo 运行以下命令安装依赖：
    echo pip3 install -r requirements.txt
    echo.
    pause
)
