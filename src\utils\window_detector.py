#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
窗口检测工具模块
用于检测和获取Windows窗口信息
"""

import win32gui
import win32con
import win32api
from typing import List, Tuple, Optional, Dict
import time

class WindowDetector:
    """Windows窗口检测器"""
    
    def __init__(self):
        self.windows = []
        
    def get_all_windows(self) -> List[Dict]:
        """获取所有可见窗口列表"""
        self.windows = []
        win32gui.EnumWindows(self._enum_window_callback, None)
        return self.windows
    
    def _enum_window_callback(self, hwnd, extra):
        """窗口枚举回调函数"""
        if win32gui.IsWindowVisible(hwnd):
            window_text = win32gui.GetWindowText(hwnd)
            if window_text:  # 只获取有标题的窗口
                rect = win32gui.GetWindowRect(hwnd)
                window_info = {
                    'hwnd': hwnd,
                    'title': window_text,
                    'rect': rect,  # (left, top, right, bottom)
                    'width': rect[2] - rect[0],
                    'height': rect[3] - rect[1]
                }
                self.windows.append(window_info)
    
    def find_window_by_title(self, title_keyword: str) -> List[Dict]:
        """根据标题关键词查找窗口"""
        matching_windows = []
        for window in self.get_all_windows():
            if title_keyword.lower() in window['title'].lower():
                matching_windows.append(window)
        return matching_windows
    
    def get_window_rect(self, hwnd: int) -> Optional[Tuple[int, int, int, int]]:
        """获取指定窗口的矩形区域"""
        try:
            return win32gui.GetWindowRect(hwnd)
        except:
            return None
    
    def is_window_valid(self, hwnd: int) -> bool:
        """检查窗口是否仍然有效"""
        try:
            return win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd)
        except:
            return False
    
    def bring_window_to_front(self, hwnd: int) -> bool:
        """将窗口置于前台"""
        try:
            win32gui.SetForegroundWindow(hwnd)
            return True
        except:
            return False
    
    def get_window_client_rect(self, hwnd: int) -> Optional[Tuple[int, int, int, int]]:
        """获取窗口客户区域坐标（相对于屏幕）"""
        try:
            # 获取客户区域大小
            client_rect = win32gui.GetClientRect(hwnd)
            # 将客户区域坐标转换为屏幕坐标
            point = win32gui.ClientToScreen(hwnd, (0, 0))
            return (
                point[0],  # left
                point[1],  # top
                point[0] + client_rect[2],  # right
                point[1] + client_rect[3]   # bottom
            )
        except:
            return None
