#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAPTCHA警报系统演示脚本
展示新的CAPTCHA检测警报功能和冷却机制
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_basic_alerts():
    """演示基本警报功能"""
    print("🚨 演示基本警报功能")
    print("-" * 40)
    
    try:
        from src.utils.notification_system import NotificationSystem
        
        # 创建通知系统
        notification_system = NotificationSystem()
        
        # 设置短冷却期用于演示
        notification_system.set_cooldown_period("demo_captcha", 5)  # 5秒冷却期
        
        print("1. 发送第一个CAPTCHA警报...")
        result1 = notification_system.send_captcha_alert("演示：检测到验证码弹窗！")
        print(f"   结果: {'✅ 成功' if result1 else '❌ 失败'}")
        
        print("\n2. 立即发送第二个警报（应该被冷却机制阻止）...")
        result2 = notification_system.send_notification(
            notification_key="captcha_detected",
            title="验证码检测",
            message="重复的验证码警报",
            enable_popup=False  # 禁用弹窗避免干扰
        )
        print(f"   结果: {'✅ 成功' if result2 else '⏰ 被冷却机制阻止'}")
        
        print("\n3. 等待冷却期结束...")
        for i in range(5, 0, -1):
            print(f"   倒计时: {i}秒", end='\r')
            time.sleep(1)
        print("   倒计时: 完成！")
        
        print("\n4. 冷却期后发送警报...")
        result3 = notification_system.send_notification(
            notification_key="captcha_detected",
            title="验证码检测",
            message="冷却期后的验证码警报",
            enable_popup=False
        )
        print(f"   结果: {'✅ 成功' if result3 else '❌ 失败'}")
        
        print("\n5. 演示强制发送（忽略冷却期）...")
        result4 = notification_system.send_notification(
            notification_key="captcha_detected",
            title="强制通知",
            message="这是强制发送的警报",
            enable_popup=False,
            force=True
        )
        print(f"   结果: {'✅ 成功' if result4 else '❌ 失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本警报演示失败: {e}")
        return False


def demo_captcha_service():
    """演示CAPTCHA服务功能"""
    print("\n🔧 演示CAPTCHA服务功能")
    print("-" * 40)
    
    try:
        from src.services.captcha_service import CaptchaService
        
        # 创建CAPTCHA服务
        captcha_service = CaptchaService()
        
        print("1. 显示当前配置...")
        status = captcha_service.get_notification_status()
        captcha_status = status.get("captcha_detected", {})
        cooldown_period = captcha_status.get("cooldown_period", 0)
        print(f"   当前冷却期: {cooldown_period // 60}分钟")
        
        print("\n2. 修改警报设置...")
        # 设置短冷却期用于演示
        captcha_service.update_alert_cooldown(1)  # 1分钟
        captcha_service.set_sound_alert_enabled(True)
        captcha_service.set_popup_alert_enabled(False)  # 禁用弹窗避免干扰
        print("   ✅ 设置已更新")
        
        print("\n3. 模拟CAPTCHA检测...")
        # 直接调用内部方法来模拟CAPTCHA检测
        captcha_service._on_captcha_detected()
        print("   ✅ CAPTCHA警报已发送")
        
        print("\n4. 立即再次模拟检测（应该被冷却）...")
        captcha_service._on_captcha_detected()
        print("   ⏰ 第二次警报被冷却机制处理")
        
        print("\n5. 显示更新后的状态...")
        new_status = captcha_service.get_notification_status()
        captcha_status = new_status.get("captcha_detected", {})
        remaining = captcha_status.get("remaining_cooldown", 0)
        print(f"   剩余冷却时间: {remaining:.0f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ CAPTCHA服务演示失败: {e}")
        return False


def demo_configuration():
    """演示配置功能"""
    print("\n⚙️ 演示配置功能")
    print("-" * 40)
    
    try:
        from src.config.settings import Settings
        
        # 创建设置实例
        settings = Settings()
        
        print("1. 显示当前CAPTCHA配置...")
        captcha_settings = settings.get_captcha_settings()
        
        important_settings = [
            ('captcha_detection', '验证码检测'),
            ('alert_cooldown_minutes', '警报冷却期(分钟)'),
            ('alert_sound_enabled', '声音警报'),
            ('alert_popup_enabled', '弹窗警报')
        ]
        
        for key, name in important_settings:
            value = captcha_settings.get(key, '未设置')
            print(f"   {name}: {value}")
        
        print("\n2. 修改配置...")
        original_cooldown = captcha_settings.get('alert_cooldown_minutes', 5)
        
        # 临时修改配置
        settings.set_alert_cooldown(3)
        settings.set_alert_sound_enabled(True)
        settings.set_alert_popup_enabled(False)
        
        print("   ✅ 配置已修改")
        
        print("\n3. 验证配置更改...")
        new_settings = settings.get_captcha_settings()
        if new_settings['alert_cooldown_minutes'] == 3:
            print("   ✅ 冷却期配置更新成功")
        else:
            print("   ❌ 冷却期配置更新失败")
        
        print("\n4. 恢复原始配置...")
        settings.set_alert_cooldown(original_cooldown)
        print(f"   ✅ 已恢复冷却期为: {original_cooldown}分钟")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置演示失败: {e}")
        return False


def show_feature_summary():
    """显示功能总结"""
    print("\n📋 新功能总结")
    print("=" * 50)
    
    features = [
        "✅ 统一的CAPTCHA检测架构",
        "✅ 可配置的警报冷却机制",
        "✅ 多种通知方式（声音、弹窗、日志）",
        "✅ 灵活的配置系统",
        "✅ 强制发送选项",
        "✅ 实时状态监控",
        "✅ 线程安全的通知系统"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print("\n🔧 配置选项:")
    config_options = [
        "alert_cooldown_minutes - 警报冷却期（分钟）",
        "alert_sound_enabled - 声音警报开关",
        "alert_popup_enabled - 弹窗警报开关"
    ]
    
    for option in config_options:
        print(f"  • {option}")
    
    print("\n💡 使用建议:")
    tips = [
        "设置合适的冷却期避免警报骚扰（推荐5-10分钟）",
        "在测试环境中禁用弹窗警报",
        "使用强制发送处理紧急情况",
        "定期检查通知状态确保系统正常"
    ]
    
    for tip in tips:
        print(f"  • {tip}")


def main():
    """主演示函数"""
    print("🎯 CAPTCHA警报系统演示")
    print("=" * 50)
    print("本演示将展示新的CAPTCHA检测警报功能")
    print("包括冷却机制、配置管理和通知系统")
    print()
    
    # 运行演示
    demos = [
        ("基本警报功能", demo_basic_alerts),
        ("CAPTCHA服务", demo_captcha_service),
        ("配置管理", demo_configuration)
    ]
    
    results = []
    for demo_name, demo_func in demos:
        try:
            result = demo_func()
            results.append((demo_name, result))
        except Exception as e:
            print(f"❌ {demo_name}演示异常: {e}")
            results.append((demo_name, False))
    
    # 显示演示结果
    print("\n📊 演示结果:")
    print("-" * 30)
    
    success_count = 0
    for demo_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {demo_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(results)} 个演示成功")
    
    # 显示功能总结
    show_feature_summary()
    
    if success_count == len(results):
        print("\n🎉 所有演示成功！CAPTCHA警报系统已准备就绪。")
    else:
        print("\n⚠️ 部分演示失败，请检查相关功能。")
    
    return success_count == len(results)


if __name__ == "__main__":
    success = main()
    print(f"\n按回车键退出...")
    input()
    sys.exit(0 if success else 1)
