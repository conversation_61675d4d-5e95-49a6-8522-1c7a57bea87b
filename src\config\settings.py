#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
用于保存和加载程序设置
"""

import configparser
import os
from typing import Tuple, Optional, Dict, Any
import json

class Settings:
    """设置管理器"""
    
    def __init__(self, config_file: str = "config.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_settings()
        
    def load_settings(self):
        """加载设置"""
        if os.path.exists(self.config_file):
            try:
                self.config.read(self.config_file, encoding='utf-8')
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                self._create_default_config()
        else:
            self._create_default_config()
            
    def _create_default_config(self):
        """创建默认配置"""
        self.config['WINDOW'] = {
            'last_window_title': '',
            'last_window_hwnd': '0',
            'target_region': '0,0,0,0'
        }
        
        self.config['CLICKING'] = {
            'min_interval': '1.0',
            'max_interval': '3.0',
            'click_margin': '10',
            'click_mode': 'normal',
            'response_detection': 'False'
        }
        
        self.config['GUI'] = {
            'window_width': '600',
            'window_height': '500',
            'always_on_top': 'False'
        }
        
        self.save_settings()
        
    def save_settings(self):
        """保存设置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            
    def get_target_region(self) -> Optional[Tuple[int, int, int, int]]:
        """获取目标区域"""
        try:
            region_str = self.config.get('WINDOW', 'target_region', fallback='0,0,0,0')
            coords = [int(x.strip()) for x in region_str.split(',')]
            if len(coords) == 4 and any(coords):
                return tuple(coords)
        except:
            pass
        return None
        
    def set_target_region(self, region: Tuple[int, int, int, int]):
        """设置目标区域"""
        region_str = f"{region[0]},{region[1]},{region[2]},{region[3]}"
        self.config.set('WINDOW', 'target_region', region_str)
        self.save_settings()
        
    def get_last_window_info(self) -> Dict[str, Any]:
        """获取上次使用的窗口信息"""
        return {
            'title': self.config.get('WINDOW', 'last_window_title', fallback=''),
            'hwnd': self.config.getint('WINDOW', 'last_window_hwnd', fallback=0)
        }
        
    def set_last_window_info(self, title: str, hwnd: int):
        """设置上次使用的窗口信息"""
        self.config.set('WINDOW', 'last_window_title', title)
        self.config.set('WINDOW', 'last_window_hwnd', str(hwnd))
        self.save_settings()
        
    def get_click_interval(self) -> Tuple[float, float]:
        """获取点击间隔"""
        min_interval = self.config.getfloat('CLICKING', 'min_interval', fallback=1.0)
        max_interval = self.config.getfloat('CLICKING', 'max_interval', fallback=3.0)
        return min_interval, max_interval
        
    def set_click_interval(self, min_interval: float, max_interval: float):
        """设置点击间隔"""
        self.config.set('CLICKING', 'min_interval', str(min_interval))
        self.config.set('CLICKING', 'max_interval', str(max_interval))
        self.save_settings()
        
    def get_gui_settings(self) -> Dict[str, Any]:
        """获取GUI设置"""
        return {
            'width': self.config.getint('GUI', 'window_width', fallback=600),
            'height': self.config.getint('GUI', 'window_height', fallback=500),
            'always_on_top': self.config.getboolean('GUI', 'always_on_top', fallback=False)
        }

    def get_captcha_settings(self) -> dict:
        """获取验证码检测设置"""
        return {
            'captcha_detection': self.config.getboolean('CAPTCHA', 'captcha_detection', fallback=False),
            'captcha_template': self.config.get('CAPTCHA', 'captcha_template', fallback='templates/captcha_default.png'),
            'captcha_threshold': self.config.getfloat('CAPTCHA', 'captcha_threshold', fallback=0.8),
            'captcha_interval': self.config.getfloat('CAPTCHA', 'captcha_interval', fallback=2.0),
            'alert_cooldown_minutes': self.config.getint('CAPTCHA', 'alert_cooldown_minutes', fallback=5),
            'alert_sound_enabled': self.config.getboolean('CAPTCHA', 'alert_sound_enabled', fallback=True),
            'alert_popup_enabled': self.config.getboolean('CAPTCHA', 'alert_popup_enabled', fallback=True)
        }

    def set_captcha_detection(self, enabled: bool):
        """设置验证码检测开关"""
        if 'CAPTCHA' not in self.config:
            self.config.add_section('CAPTCHA')
        self.config.set('CAPTCHA', 'captcha_detection', str(enabled))
        self.save_settings()

    def set_captcha_template(self, template_path: str):
        """设置验证码模板路径"""
        if 'CAPTCHA' not in self.config:
            self.config.add_section('CAPTCHA')
        self.config.set('CAPTCHA', 'captcha_template', template_path)
        self.save_settings()

    def set_alert_cooldown(self, minutes: int):
        """设置警报冷却期（分钟）"""
        if 'CAPTCHA' not in self.config:
            self.config.add_section('CAPTCHA')
        self.config.set('CAPTCHA', 'alert_cooldown_minutes', str(minutes))
        self.save_settings()

    def set_alert_sound_enabled(self, enabled: bool):
        """设置声音警报开关"""
        if 'CAPTCHA' not in self.config:
            self.config.add_section('CAPTCHA')
        self.config.set('CAPTCHA', 'alert_sound_enabled', str(enabled))
        self.save_settings()

    def set_alert_popup_enabled(self, enabled: bool):
        """设置弹窗警报开关"""
        if 'CAPTCHA' not in self.config:
            self.config.add_section('CAPTCHA')
        self.config.set('CAPTCHA', 'alert_popup_enabled', str(enabled))
        self.save_settings()
        
    def set_gui_settings(self, width: int, height: int, always_on_top: bool):
        """设置GUI设置"""
        self.config.set('GUI', 'window_width', str(width))
        self.config.set('GUI', 'window_height', str(height))
        self.config.set('GUI', 'always_on_top', str(always_on_top))
        self.save_settings()
        
    def get_click_margin(self) -> int:
        """获取点击边距"""
        return self.config.getint('CLICKING', 'click_margin', fallback=10)
        
    def set_click_margin(self, margin: int):
        """设置点击边距"""
        self.config.set('CLICKING', 'click_margin', str(margin))
        self.save_settings()

    def get_click_mode(self) -> str:
        """获取点击模式"""
        return self.config.get('CLICKING', 'click_mode', fallback='normal')

    def set_click_mode(self, mode: str):
        """设置点击模式"""
        self.config.set('CLICKING', 'click_mode', mode)
        self.save_settings()

    def get_response_detection(self) -> bool:
        """获取响应检测设置"""
        return self.config.getboolean('CLICKING', 'response_detection', fallback=False)

    def set_response_detection(self, enabled: bool):
        """设置响应检测"""
        self.config.set('CLICKING', 'response_detection', str(enabled))
        self.save_settings()
