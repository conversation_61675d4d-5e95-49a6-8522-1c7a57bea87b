#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏响应检测模块
用于检测游戏是否对鼠标点击产生响应
"""

import cv2
import numpy as np
import win32gui
import win32ui
import win32con
import time
from typing import Tuple, Optional, Callable
import threading

class GameResponseDetector:
    """游戏响应检测器"""
    
    def __init__(self):
        self.is_monitoring = False
        self.monitor_thread = None
        self.status_callback = None
        
        # 检测参数
        self.detection_enabled = False
        self.last_screenshot = None
        self.change_threshold = 0.05  # 变化阈值（5%）
        self.detection_delay = 0.5    # 检测延迟（秒）
        
        # 响应统计
        self.total_clicks = 0
        self.detected_responses = 0
        self.response_rate = 0.0
        
    def set_status_callback(self, callback: Callable[[str], None]):
        """设置状态回调函数"""
        self.status_callback = callback
        
    def _log_status(self, message: str):
        """记录状态信息"""
        if self.status_callback:
            self.status_callback(f"[响应检测] {message}")
            
    def enable_detection(self, enabled: bool = True):
        """启用/禁用响应检测"""
        self.detection_enabled = enabled
        if enabled:
            self._log_status("游戏响应检测已启用")
        else:
            self._log_status("游戏响应检测已禁用")
            
    def start_monitoring(self, hwnd: int, region: Tuple[int, int, int, int]):
        """开始监控游戏窗口"""
        if not self.detection_enabled:
            return
            
        if self.is_monitoring:
            return
            
        self.hwnd = hwnd
        self.region = region
        self.is_monitoring = True
        
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop, 
            daemon=True
        )
        self.monitor_thread.start()
        self._log_status("开始监控游戏响应")
        
    def stop_monitoring(self):
        """停止监控"""
        if self.is_monitoring:
            self.is_monitoring = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=1.0)
            self._log_status("停止监控游戏响应")
            
    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 定期更新基准截图
                if self.last_screenshot is None:
                    self.last_screenshot = self._capture_region()
                    
                time.sleep(1.0)  # 每秒更新一次基准
                
                # 更新基准截图（缓慢更新，避免误判）
                current_screenshot = self._capture_region()
                if current_screenshot is not None and self.last_screenshot is not None:
                    # 使用加权平均更新基准
                    self.last_screenshot = cv2.addWeighted(
                        self.last_screenshot, 0.9, 
                        current_screenshot, 0.1, 0
                    )
                    
            except Exception as e:
                self._log_status(f"监控过程中发生错误: {e}")
                time.sleep(1.0)
                
    def _capture_region(self) -> Optional[np.ndarray]:
        """捕获指定区域的截图"""
        try:
            # 获取窗口DC
            hwndDC = win32gui.GetWindowDC(self.hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            
            # 计算区域大小
            left, top, right, bottom = self.region
            width = right - left
            height = bottom - top
            
            # 创建位图
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)
            
            # 复制屏幕内容
            saveDC.BitBlt((0, 0), (width, height), mfcDC, (left, top), win32con.SRCCOPY)
            
            # 转换为numpy数组
            bmpinfo = saveBitMap.GetInfo()
            bmpstr = saveBitMap.GetBitmapBits(True)
            
            img = np.frombuffer(bmpstr, dtype='uint8')
            img.shape = (height, width, 4)
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
            
            # 清理资源
            win32gui.DeleteObject(saveBitMap.GetHandle())
            saveDC.DeleteDC()
            mfcDC.DeleteDC()
            win32gui.ReleaseDC(self.hwnd, hwndDC)
            
            return img
            
        except Exception as e:
            self._log_status(f"截图失败: {e}")
            return None
            
    def detect_response_after_click(self, click_x: int, click_y: int, timeout: float = None) -> bool:
        """点击后检测游戏响应"""
        if not self.detection_enabled or not self.is_monitoring:
            return True  # 如果未启用检测，假设响应正常

        try:
            self.total_clicks += 1

            # 使用传入的超时时间或默认检测延迟
            detection_delay = timeout if timeout is not None else self.detection_delay
            detection_delay = min(detection_delay, 2.0)  # 最大不超过2秒

            # 等待游戏响应
            time.sleep(detection_delay)

            # 捕获点击后的截图
            after_screenshot = self._capture_region()

            if after_screenshot is None or self.last_screenshot is None:
                return True  # 无法检测，假设正常

            # 计算图像差异
            diff = cv2.absdiff(self.last_screenshot, after_screenshot)
            diff_gray = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)

            # 计算变化百分比
            total_pixels = diff_gray.shape[0] * diff_gray.shape[1]
            changed_pixels = np.count_nonzero(diff_gray > 30)  # 阈值30
            change_ratio = changed_pixels / total_pixels

            # 判断是否有响应
            has_response = change_ratio > self.change_threshold

            if has_response:
                self.detected_responses += 1
                self._log_status(f"检测到游戏响应 (变化: {change_ratio:.3f})")
            else:
                self._log_status(f"未检测到明显响应 (变化: {change_ratio:.3f})")

            # 更新响应率
            self.response_rate = self.detected_responses / self.total_clicks
            
            return has_response
            
        except Exception as e:
            self._log_status(f"响应检测失败: {e}")
            return True  # 检测失败，假设正常
            
    def get_response_statistics(self) -> dict:
        """获取响应统计信息"""
        return {
            'total_clicks': self.total_clicks,
            'detected_responses': self.detected_responses,
            'response_rate': self.response_rate,
            'detection_enabled': self.detection_enabled,
            'is_monitoring': self.is_monitoring
        }
        
    def reset_statistics(self):
        """重置统计信息"""
        self.total_clicks = 0
        self.detected_responses = 0
        self.response_rate = 0.0
        self._log_status("响应统计信息已重置")
        
    def configure_detection(self, **kwargs):
        """配置检测参数"""
        if 'change_threshold' in kwargs:
            self.change_threshold = kwargs['change_threshold']
        if 'detection_delay' in kwargs:
            self.detection_delay = kwargs['detection_delay']
            
        self._log_status("检测参数已更新")
        
    def suggest_click_mode(self) -> str:
        """根据响应率建议点击模式"""
        if self.total_clicks < 10:
            return "normal"  # 数据不足，使用默认模式
            
        if self.response_rate < 0.3:
            return "hold"  # 响应率低，建议保持点击
        elif self.response_rate < 0.6:
            return "continuous"  # 响应率中等，建议连续点击
        else:
            return "normal"  # 响应率高，使用普通点击
