#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知系统测试脚本
测试CAPTCHA检测的警报通知系统和冷却机制
"""

import sys
import os
import time
import threading

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_notification_system():
    """测试通知系统"""
    print("🧪 开始测试通知系统...")
    
    try:
        from src.utils.notification_system import NotificationSystem, NotificationType
        
        # 创建通知系统实例
        notification_system = NotificationSystem()
        print("✅ 通知系统创建成功")
        
        # 测试基本通知功能
        print("\n📢 测试基本通知功能...")
        
        # 发送测试通知
        result = notification_system.send_notification(
            notification_key="test_notification",
            title="测试通知",
            message="这是一个测试通知",
            level="info",
            enable_sound=True,
            enable_popup=False,  # 禁用弹窗避免干扰测试
            enable_log=True,
            enable_callback=True
        )
        
        if result:
            print("✅ 基本通知发送成功")
        else:
            print("❌ 基本通知发送失败")
            
        # 测试冷却机制
        print("\n⏰ 测试冷却机制...")
        
        # 设置短冷却期用于测试
        notification_system.set_cooldown_period("test_cooldown", 3)  # 3秒冷却期
        
        # 第一次发送
        result1 = notification_system.send_notification(
            notification_key="test_cooldown",
            title="冷却测试",
            message="第一次通知",
            enable_popup=False
        )
        print(f"第一次通知: {'成功' if result1 else '失败'}")
        
        # 立即第二次发送（应该被冷却机制阻止）
        result2 = notification_system.send_notification(
            notification_key="test_cooldown",
            title="冷却测试",
            message="第二次通知（应该被阻止）",
            enable_popup=False
        )
        print(f"第二次通知（立即发送）: {'成功' if result2 else '被冷却机制阻止'}")
        
        # 等待冷却期结束
        print("等待冷却期结束（3秒）...")
        time.sleep(3.5)
        
        # 冷却期后再次发送
        result3 = notification_system.send_notification(
            notification_key="test_cooldown",
            title="冷却测试",
            message="第三次通知（冷却期后）",
            enable_popup=False
        )
        print(f"第三次通知（冷却期后）: {'成功' if result3 else '失败'}")
        
        # 测试CAPTCHA专用警报
        print("\n🚨 测试CAPTCHA警报...")
        
        # 设置CAPTCHA冷却期
        notification_system.set_cooldown_period("captcha_detected", 2)  # 2秒冷却期
        
        # 发送CAPTCHA警报
        captcha_result1 = notification_system.send_captcha_alert("测试CAPTCHA检测")
        print(f"CAPTCHA警报1: {'成功' if captcha_result1 else '失败'}")
        
        # 立即再次发送（应该被阻止）
        captcha_result2 = notification_system.send_captcha_alert("重复CAPTCHA检测")
        print(f"CAPTCHA警报2（立即发送）: {'成功' if captcha_result2 else '被冷却机制阻止'}")
        
        # 测试强制发送
        print("\n🔥 测试强制发送...")
        force_result = notification_system.send_notification(
            notification_key="captcha_detected",
            title="强制通知",
            message="这是强制发送的通知",
            enable_popup=False,
            force=True
        )
        print(f"强制通知: {'成功' if force_result else '失败'}")
        
        # 显示冷却状态
        print("\n📊 当前冷却状态:")
        status = notification_system.get_cooldown_status()
        for key, info in status.items():
            remaining = info['remaining_cooldown']
            in_cooldown = info['is_in_cooldown']
            print(f"  {key}: {'冷却中' if in_cooldown else '可用'} (剩余: {remaining:.1f}秒)")
            
        print("\n✅ 通知系统测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 通知系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_captcha_service():
    """测试CAPTCHA服务"""
    print("\n🧪 开始测试CAPTCHA服务...")
    
    try:
        from src.services.captcha_service import CaptchaService
        
        # 创建CAPTCHA服务实例
        captcha_service = CaptchaService()
        print("✅ CAPTCHA服务创建成功")
        
        # 测试通知状态
        print("\n📊 获取通知状态...")
        status = captcha_service.get_notification_status()
        print(f"通知状态: {len(status)}个通知键")
        
        # 测试设置冷却期
        print("\n⚙️ 测试设置冷却期...")
        captcha_service.update_alert_cooldown(1)  # 设置1分钟冷却期
        print("✅ 冷却期设置成功")
        
        # 测试声音和弹窗开关
        print("\n🔊 测试声音和弹窗开关...")
        captcha_service.set_sound_alert_enabled(True)
        captcha_service.set_popup_alert_enabled(False)  # 禁用弹窗避免干扰
        print("✅ 声音和弹窗设置成功")
        
        print("\n✅ CAPTCHA服务测试完成")
        return True
        
    except Exception as e:
        print(f"❌ CAPTCHA服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_configuration():
    """测试配置系统"""
    print("\n🧪 开始测试配置系统...")
    
    try:
        from src.config.settings import Settings
        
        # 创建设置实例
        settings = Settings()
        print("✅ 设置系统创建成功")
        
        # 测试获取CAPTCHA设置
        print("\n📋 测试获取CAPTCHA设置...")
        captcha_settings = settings.get_captcha_settings()
        
        expected_keys = [
            'captcha_detection', 'captcha_template', 'captcha_threshold', 
            'captcha_interval', 'alert_cooldown_minutes', 'alert_sound_enabled', 
            'alert_popup_enabled'
        ]
        
        for key in expected_keys:
            if key in captcha_settings:
                print(f"  ✅ {key}: {captcha_settings[key]}")
            else:
                print(f"  ❌ 缺少配置项: {key}")
                
        # 测试设置警报配置
        print("\n⚙️ 测试设置警报配置...")
        original_cooldown = captcha_settings.get('alert_cooldown_minutes', 5)
        
        # 设置新的冷却期
        settings.set_alert_cooldown(10)
        settings.set_alert_sound_enabled(True)
        settings.set_alert_popup_enabled(True)
        
        # 验证设置是否生效
        new_settings = settings.get_captcha_settings()
        if new_settings['alert_cooldown_minutes'] == 10:
            print("✅ 冷却期设置成功")
        else:
            print("❌ 冷却期设置失败")
            
        # 恢复原始设置
        settings.set_alert_cooldown(original_cooldown)
        print(f"✅ 已恢复原始冷却期: {original_cooldown}分钟")
        
        print("\n✅ 配置系统测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 配置系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始CAPTCHA警报系统测试")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("通知系统", test_notification_system),
        ("CAPTCHA服务", test_captcha_service),
        ("配置系统", test_configuration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！CAPTCHA警报系统工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查相关功能。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
