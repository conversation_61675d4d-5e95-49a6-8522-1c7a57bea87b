@echo off
echo ========================================
echo 梦幻西游自动走路工具 - 依赖安装脚本
echo ========================================
echo.

echo 正在安装Python依赖库...
echo.

REM 尝试使用pip3安装
pip3 install -r requirements.txt

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ 依赖安装成功！
    echo.
    echo 现在可以运行 start.bat 启动程序
) else (
    echo.
    echo ✗ 安装失败，尝试使用pip...
    pip install -r requirements.txt
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ✓ 依赖安装成功！
        echo.
        echo 现在可以运行 start.bat 启动程序
    ) else (
        echo.
        echo ✗ 依赖安装失败！
        echo 请检查Python环境是否正确安装
    )
)

echo.
pause
