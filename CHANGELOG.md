# 更新日志

## v1.1.0 - 2025-07-27 - 增强版

### 🔥 重大更新：鼠标模拟优化

#### 新增功能
- **增强鼠标操作模块** (`src/core/enhanced_mouse.py`)
  - 实现更真实的鼠标按下/弹起延迟（50-150ms随机）
  - 添加点击前后的停顿时间，模拟人类反应时间
  - 支持平滑鼠标移动轨迹，使用贝塞尔曲线算法
  - 添加轻微随机偏移和抖动，模拟人手自然颤动

- **多种点击模式**
  - **普通点击**：标准的单次点击，适合大多数情况
  - **连续点击**：快速连续多次点击（3-7次），适合需要持续移动的场景
  - **保持点击**：在指定时间内（500-1500ms）持续点击，模拟按住鼠标的效果
  - **平滑移动**：先平滑移动鼠标到目标位置再点击，最接近人类操作

- **游戏响应检测系统** (`src/utils/game_response_detector.py`)
  - 实验性功能：通过屏幕截图对比检测游戏是否响应点击
  - 自动统计响应率并提供点击模式建议
  - 支持自适应调整点击策略

#### 界面改进
- 新增点击模式选择界面，支持四种点击模式
- 添加游戏响应检测开关（实验性功能）
- 优化界面布局，窗口尺寸调整为750x700
- 更新程序标题为"v1.1 - 增强版"

#### 配置管理
- 新增点击模式配置保存/加载
- 新增响应检测设置保存/加载
- 扩展配置文件支持更多参数

#### 技术改进
- 重构自动点击器，集成增强鼠标操作
- 优化安全检查机制，与新的点击模式兼容
- 改进状态监控，包含响应检测统计信息
- 添加详细的日志输出，便于调试

#### 测试和文档
- 新增增强功能测试脚本 (`test_enhanced_features.py`)
- 更新README文档，详细说明新功能
- 创建更新日志文档

### 🐛 修复问题
- 修复了原有鼠标点击可能在某些游戏中无效的问题
- 优化了点击频率控制，避免过度频繁操作
- 改进了错误处理机制

### ⚡ 性能优化
- 优化鼠标移动算法，减少CPU占用
- 改进多线程处理，提高响应速度
- 优化内存使用，减少资源占用

---

## v1.0.0 - 2025-07-27 - 初始版本

### 🎉 首次发布

#### 核心功能
- **窗口检测与选择**：自动检测系统窗口，支持选择目标游戏窗口
- **区域选择**：可视化屏幕区域选择，精确定义点击范围
- **自动点击**：在指定区域内随机位置进行鼠标点击
- **安全机制**：多重安全检查，紧急停止功能
- **配置管理**：自动保存和加载用户设置
- **用户界面**：简洁直观的GUI界面

#### 安全特性
- 鼠标左上角紧急停止
- Ctrl+Alt+S快捷键紧急停止
- 窗口边界检查
- 点击频率限制
- 异常处理机制

#### 项目结构
- 模块化设计，清晰的代码架构
- 完整的文档和使用说明
- 便捷的安装和启动脚本

---

## 开发计划

### v1.2.0 - 计划中
- 战斗检测系统
- 智能暂停恢复功能
- 路径记录和回放
- 更多自定义选项

### 长期计划
- 多游戏支持
- 云端配置同步
- 插件系统
- 机器学习优化
