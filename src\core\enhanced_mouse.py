#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的鼠标操作模块
实现更真实的人类鼠标操作模拟
"""

import pyautogui
import win32api
import win32con
import win32gui
import random
import time
import math
from typing import Tuple, List, Optional, Callable
from enum import Enum
from ..utils.logger import get_logger

class ClickMode(Enum):
    """点击模式枚举"""
    NORMAL = "normal"           # 普通点击
    CONTINUOUS = "continuous"   # 连续点击
    HOLD = "hold"              # 保持点击
    SMOOTH = "smooth"          # 平滑移动点击

class EnhancedMouse:
    """增强的鼠标操作器"""
    
    def __init__(self):
        # 基础设置
        self.click_mode = ClickMode.NORMAL
        self.status_callback = None
        self.logger = get_logger()

        # 权限和兼容性检查
        self.use_win32_api = True
        self.fallback_to_pyautogui = False
        
        # 点击行为参数
        self.mouse_down_delay = (20, 100)      # 鼠标按下到弹起的延迟(ms)
        self.pre_click_delay = (100, 300)     # 点击前停顿时间(ms)
        self.post_click_delay = (50, 200)     # 点击后停顿时间(ms)
        
        # 连续点击参数
        self.continuous_clicks = (3, 7)        # 连续点击次数范围
        self.click_interval = (80, 200)        # 连续点击间隔(ms)
        
        # 保持点击参数
        self.hold_duration = (500, 1500)       # 保持点击时长(ms)
        self.hold_clicks = (5, 12)            # 保持期间点击次数
        
        # 平滑移动参数 - 进一步优化速度
        self.move_steps = (3, 8)              # 移动步数 - 大幅减少步数提高速度
        self.move_speed = (0.001, 0.003)      # 每步延迟(s) - 进一步减少延迟
        self.jitter_range = (1, 2)            # 抖动范围(像素) - 减少抖动
        
        # 禁用pyautogui的自动延迟，我们手动控制
        pyautogui.PAUSE = 0

        # 快速模式设置
        self.fast_mode = False  # 是否启用快速平滑移动

        # 初始化时检查权限和API可用性
        self._check_api_availability()
        
    def set_status_callback(self, callback: Callable[[str], None]):
        """设置状态回调函数"""
        self.status_callback = callback
        
    def _log_status(self, message: str):
        """记录状态信息"""
        if self.status_callback:
            self.status_callback(f"[增强鼠标] {message}")

    def _check_api_availability(self):
        """检查Win32 API可用性和权限"""
        try:
            # 测试SetCursorPos API
            current_pos = win32gui.GetCursorPos()
            test_result = win32api.SetCursorPos(current_pos)

            self.logger.log_win32_api_call(
                "SetCursorPos",
                {"position": current_pos},
                test_result
            )

            if test_result == 0:  # 成功
                self.use_win32_api = True
                self.fallback_to_pyautogui = False
                self._log_status("Win32 API可用，使用原生API")
            else:
                raise Exception("SetCursorPos返回非零值")

        except Exception as e:
            error_code = win32api.GetLastError()
            self.logger.log_win32_api_call(
                "SetCursorPos",
                {"test": True},
                False,
                error_code
            )

            self.use_win32_api = False
            self.fallback_to_pyautogui = True
            self._log_status(f"Win32 API不可用，回退到pyautogui: {e}")

            # 记录详细错误信息
            self.logger.error_logger.error(f"Win32 API初始化失败: {e}, 错误码: {error_code}")
            
    def set_click_mode(self, mode: ClickMode):
        """设置点击模式"""
        self.click_mode = mode
        mode_name = mode.value if hasattr(mode, 'value') else str(mode)
        self._log_status(f"点击模式设置为: {mode_name}")

    def set_fast_mode(self, enabled: bool):
        """设置快速平滑移动模式"""
        self.fast_mode = enabled
        if enabled:
            # 快速模式参数
            self.move_steps = (2, 4)
            self.move_speed = (0.0005, 0.002)
            self.jitter_range = (0, 1)
            self._log_status("已启用快速平滑移动模式")
        else:
            # 恢复正常参数
            self.move_steps = (3, 8)
            self.move_speed = (0.001, 0.003)
            self.jitter_range = (1, 2)
            self._log_status("已禁用快速平滑移动模式")
        
    def _random_delay(self, delay_range: Tuple[int, int]):
        """生成随机延迟并等待"""
        delay_ms = random.randint(*delay_range)
        time.sleep(delay_ms / 1000.0)
        
    def _smooth_move_to(self, target_x: int, target_y: int) -> bool:
        """平滑移动鼠标到目标位置"""
        try:
            # 验证坐标有效性
            if not self._validate_coordinates(target_x, target_y):
                self._log_status(f"无效坐标: ({target_x}, {target_y})")
                return False

            current_x, current_y = self._get_current_position()
            if current_x is None or current_y is None:
                return False

            # 计算移动距离
            distance = math.sqrt((target_x - current_x)**2 + (target_y - current_y)**2)

            # 如果距离很小，直接移动
            if distance < 10:  # 增加直接移动的阈值
                return self._move_cursor_to(target_x, target_y)

            # 计算移动步数 - 根据距离动态调整
            if distance < 50:
                steps = 2  # 短距离用更少步数
            elif distance < 200:
                steps = random.randint(3, 5)  # 中距离
            else:
                steps = random.randint(5, 8)  # 长距离

            steps = max(2, min(steps, int(distance / 5)))  # 确保合理的步数
                
            # 生成贝塞尔曲线路径点
            path_points = self._generate_smooth_path(
                current_x, current_y, target_x, target_y, steps
            )
            
            # 沿路径移动
            for i, (x, y) in enumerate(path_points):
                # 添加轻微抖动
                jitter_x = random.randint(-self.jitter_range[1], self.jitter_range[1])
                jitter_y = random.randint(-self.jitter_range[1], self.jitter_range[1])
                
                final_x = max(0, min(x + jitter_x, pyautogui.size().width - 1))
                final_y = max(0, min(y + jitter_y, pyautogui.size().height - 1))

                if not self._move_cursor_to(final_x, final_y):
                    self._log_status(f"路径移动失败: ({final_x}, {final_y})")
                    return False
                
                # 随机移动速度 - 快速模式优化
                if i < len(path_points) - 1:  # 最后一步不延迟
                    if self.fast_mode:
                        # 快速模式：只在每隔几步才延迟
                        if i % 2 == 0:  # 每隔一步延迟一次
                            delay = random.uniform(*self.move_speed)
                            time.sleep(delay)
                    else:
                        # 正常模式：每步都延迟
                        delay = random.uniform(*self.move_speed)
                        time.sleep(delay)
                    
            return True
            
        except Exception as e:
            self._log_status(f"平滑移动失败: {e}")
            self.logger.log_mouse_operation("平滑移动", target_x, target_y, False, str(e))

            # 回退到直接移动
            return self._move_cursor_to(target_x, target_y)
                
    def _generate_smooth_path(self, start_x: int, start_y: int, 
                            end_x: int, end_y: int, steps: int) -> List[Tuple[int, int]]:
        """生成平滑的移动路径（简化的贝塞尔曲线）"""
        path_points = []
        
        # 添加中间控制点，创建轻微弧线
        mid_x = (start_x + end_x) / 2
        mid_y = (start_y + end_y) / 2
        
        # 随机偏移中间点，创建自然的弧线
        offset_range = min(50, abs(end_x - start_x) / 4, abs(end_y - start_y) / 4)
        if offset_range > 5:
            mid_x += random.uniform(-offset_range, offset_range)
            mid_y += random.uniform(-offset_range, offset_range)
            
        # 生成路径点
        for i in range(steps + 1):
            t = i / steps
            
            # 二次贝塞尔曲线
            x = (1-t)**2 * start_x + 2*(1-t)*t * mid_x + t**2 * end_x
            y = (1-t)**2 * start_y + 2*(1-t)*t * mid_y + t**2 * end_y
            
            path_points.append((int(x), int(y)))
            
        return path_points

    def _validate_coordinates(self, x: int, y: int) -> bool:
        """验证坐标是否有效"""
        try:
            screen_width, screen_height = pyautogui.size()
            return 0 <= x < screen_width and 0 <= y < screen_height
        except:
            return False

    def _get_current_position(self) -> Tuple[Optional[int], Optional[int]]:
        """获取当前鼠标位置"""
        try:
            if self.use_win32_api:
                pos = win32gui.GetCursorPos()
                return pos[0], pos[1]
            else:
                pos = pyautogui.position()
                return pos.x, pos.y
        except Exception as e:
            self._log_status(f"获取鼠标位置失败: {e}")
            return None, None

    def _move_cursor_to(self, x: int, y: int) -> bool:
        """移动鼠标到指定位置（带回退机制）"""
        try:
            if self.use_win32_api:
                # 尝试使用Win32 API
                result = win32api.SetCursorPos((x, y))

                self.logger.log_win32_api_call(
                    "SetCursorPos",
                    {"x": x, "y": y},
                    result
                )

                if result == 0:  # 成功
                    self.logger.log_mouse_operation("Win32移动", x, y, True)
                    return True
                else:
                    # API调用失败，获取错误码
                    error_code = win32api.GetLastError()
                    error_msg = f"SetCursorPos失败，错误码: {error_code}"

                    self.logger.log_win32_api_call(
                        "SetCursorPos",
                        {"x": x, "y": y},
                        result,
                        error_code
                    )

                    # 如果连续失败，切换到pyautogui
                    if not hasattr(self, '_win32_fail_count'):
                        self._win32_fail_count = 0
                    self._win32_fail_count += 1

                    if self._win32_fail_count >= 3:
                        self._log_status("Win32 API连续失败，切换到pyautogui")
                        self.use_win32_api = False
                        self.fallback_to_pyautogui = True

                    raise Exception(error_msg)

            # 使用pyautogui作为回退方案
            if self.fallback_to_pyautogui or not self.use_win32_api:
                pyautogui.moveTo(x, y)
                self.logger.log_mouse_operation("pyautogui移动", x, y, True)
                return True

        except Exception as e:
            self.logger.log_mouse_operation("鼠标移动", x, y, False, str(e))

            # 最后的回退尝试
            if self.use_win32_api:
                try:
                    pyautogui.moveTo(x, y)
                    self.logger.log_mouse_operation("回退移动", x, y, True)
                    return True
                except Exception as fallback_error:
                    self.logger.log_mouse_operation("回退移动", x, y, False, str(fallback_error))

            return False

    def _perform_raw_click(self, x: int, y: int) -> bool:
        """执行原始的鼠标点击（按下-延迟-弹起）"""
        try:
            # 验证坐标
            if not self._validate_coordinates(x, y):
                self._log_status(f"点击坐标无效: ({x}, {y})")
                return False

            # 点击前停顿
            self._random_delay(self.pre_click_delay)

            success = False

            if self.use_win32_api:
                # 使用Win32 API进行点击
                success = self._win32_click(x, y)

            if not success and (self.fallback_to_pyautogui or not self.use_win32_api):
                # 使用pyautogui作为回退
                success = self._pyautogui_click(x, y)

            # 点击后停顿
            if success:
                self._random_delay(self.post_click_delay)

            return success

        except Exception as e:
            self._log_status(f"原始点击失败: {e}")
            self.logger.log_mouse_operation("原始点击", x, y, False, str(e))
            return False

    def _win32_click(self, x: int, y: int) -> bool:
        """使用Win32 API执行点击"""
        try:
            # 移动鼠标到目标位置
            if not self._move_cursor_to(x, y):
                return False

            # 鼠标按下
            result_down = win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, x, y, 0, 0)
            self.logger.log_win32_api_call(
                "mouse_event_DOWN",
                {"x": x, "y": y, "flags": "LEFTDOWN"},
                result_down
            )

            # 按下保持延迟
            self._random_delay(self.mouse_down_delay)

            # 鼠标弹起
            result_up = win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, x, y, 0, 0)
            self.logger.log_win32_api_call(
                "mouse_event_UP",
                {"x": x, "y": y, "flags": "LEFTUP"},
                result_up
            )

            self.logger.log_mouse_operation("Win32点击", x, y, True)
            return True

        except Exception as e:
            error_code = win32api.GetLastError()
            self.logger.log_win32_api_call(
                "mouse_event",
                {"x": x, "y": y},
                False,
                error_code
            )

            self._log_status(f"Win32点击失败: {e}")
            return False

    def _pyautogui_click(self, x: int, y: int) -> bool:
        """使用pyautogui执行点击"""
        try:
            # 模拟按下保持延迟
            delay_ms = random.randint(*self.mouse_down_delay)

            pyautogui.click(x, y, duration=delay_ms/1000.0)
            self.logger.log_mouse_operation("pyautogui点击", x, y, True)
            return True

        except Exception as e:
            self._log_status(f"pyautogui点击失败: {e}")
            self.logger.log_mouse_operation("pyautogui点击", x, y, False, str(e))
            return False
            
    def enhanced_click(self, x: int, y: int) -> bool:
        """增强的点击操作"""
        try:
            if self.click_mode == ClickMode.NORMAL:
                return self._normal_click(x, y)
            elif self.click_mode == ClickMode.CONTINUOUS:
                return self._continuous_click(x, y)
            elif self.click_mode == ClickMode.HOLD:
                return self._hold_click(x, y)
            elif self.click_mode == ClickMode.SMOOTH:
                return self._smooth_click(x, y)
            else:
                return self._normal_click(x, y)
                
        except Exception as e:
            self._log_status(f"增强点击失败: {e}")
            return False
            
    def _normal_click(self, x: int, y: int) -> bool:
        """普通点击模式"""
        self._log_status(f"普通点击: ({x}, {y})")
        return self._perform_raw_click(x, y)
        
    def _continuous_click(self, x: int, y: int) -> bool:
        """连续点击模式（人类化）"""
        click_count = random.randint(*self.continuous_clicks)
        self._log_status(f"连续点击: ({x}, {y}) x{click_count}")

        # 人类化的点击节奏：开始快，然后逐渐变慢
        base_interval = self.click_interval[0]

        for i in range(click_count):
            # 添加更大的位置偏移，模拟人手不稳
            offset_range = min(5 + i, 15)  # 随着点击次数增加，偏移增大
            offset_x = random.randint(-offset_range, offset_range)
            offset_y = random.randint(-offset_range, offset_range)
            click_x = x + offset_x
            click_y = y + offset_y

            if not self._perform_raw_click(click_x, click_y):
                return False

            # 人类化的间隔：逐渐增加间隔时间
            if i < click_count - 1:
                # 基础间隔 + 递增延迟 + 随机波动
                interval_ms = base_interval * 1000 + (i * 20) + random.randint(-30, 50)
                interval_ms = max(50, interval_ms)  # 最小50ms间隔

                time.sleep(interval_ms / 1000.0)

                # 偶尔添加"思考"停顿
                if random.random() < 0.2:  # 20%概率
                    think_pause = random.randint(100, 300)
                    time.sleep(think_pause / 1000.0)

        return True
        
    def _hold_click(self, x: int, y: int) -> bool:
        """保持点击模式（人类化）"""
        duration_ms = random.randint(*self.hold_duration)
        click_count = random.randint(*self.hold_clicks)

        self._log_status(f"保持点击: ({x}, {y}) 持续{duration_ms}ms, {click_count}次点击")

        start_time = time.time()
        end_time = start_time + (duration_ms / 1000.0)

        clicks_done = 0
        last_click_time = start_time

        while time.time() < end_time and clicks_done < click_count:
            current_time = time.time()

            # 人类化的位置偏移：随时间增加而增大（模拟手部疲劳）
            fatigue_factor = min(clicks_done / 5.0, 2.0)  # 最大2倍疲劳系数
            base_offset = 3 + int(fatigue_factor * 2)

            offset_x = random.randint(-base_offset, base_offset)
            offset_y = random.randint(-base_offset, base_offset)
            click_x = x + offset_x
            click_y = y + offset_y

            if not self._perform_raw_click(click_x, click_y):
                return False

            clicks_done += 1
            last_click_time = current_time

            # 人类化的间隔计算
            if clicks_done < click_count:
                remaining_time = end_time - current_time
                remaining_clicks = click_count - clicks_done

                if remaining_clicks > 0:
                    # 基础间隔计算
                    avg_interval = remaining_time / remaining_clicks

                    # 添加人类化的变化：
                    # 1. 随机波动
                    random_factor = random.uniform(0.6, 1.4)
                    # 2. 疲劳效应（点击越多，间隔越不规律）
                    fatigue_variation = random.uniform(0.8, 1.2 + fatigue_factor * 0.3)
                    # 3. 偶尔的"犹豫"
                    hesitation = 1.0
                    if random.random() < 0.15:  # 15%概率犹豫
                        hesitation = random.uniform(1.5, 2.5)

                    next_interval = avg_interval * random_factor * fatigue_variation * hesitation
                    next_interval = max(0.05, min(next_interval, remaining_time * 0.8))

                    time.sleep(next_interval)

        return True
        
    def _smooth_click(self, x: int, y: int) -> bool:
        """平滑移动点击模式"""
        self._log_status(f"平滑点击: ({x}, {y})")
        
        # 先平滑移动到目标位置
        if not self._smooth_move_to(x, y):
            return False
            
        # 然后执行点击
        return self._perform_raw_click(x, y)

    def configure_click_behavior(self, **kwargs):
        """配置点击行为参数"""
        if 'mouse_down_delay' in kwargs:
            self.mouse_down_delay = kwargs['mouse_down_delay']
        if 'pre_click_delay' in kwargs:
            self.pre_click_delay = kwargs['pre_click_delay']
        if 'post_click_delay' in kwargs:
            self.post_click_delay = kwargs['post_click_delay']

        self._log_status("点击行为参数已更新")

    def configure_continuous_mode(self, **kwargs):
        """配置连续点击模式参数"""
        if 'continuous_clicks' in kwargs:
            self.continuous_clicks = kwargs['continuous_clicks']
        if 'click_interval' in kwargs:
            self.click_interval = kwargs['click_interval']

        self._log_status("连续点击参数已更新")

    def configure_hold_mode(self, **kwargs):
        """配置保持点击模式参数"""
        if 'hold_duration' in kwargs:
            self.hold_duration = kwargs['hold_duration']
        if 'hold_clicks' in kwargs:
            self.hold_clicks = kwargs['hold_clicks']

        self._log_status("保持点击参数已更新")

    def configure_smooth_mode(self, **kwargs):
        """配置平滑移动模式参数"""
        if 'move_steps' in kwargs:
            self.move_steps = kwargs['move_steps']
        if 'move_speed' in kwargs:
            self.move_speed = kwargs['move_speed']
        if 'jitter_range' in kwargs:
            self.jitter_range = kwargs['jitter_range']

        self._log_status("平滑移动参数已更新")

    def get_current_config(self) -> dict:
        """获取当前配置"""
        return {
            'click_mode': self.click_mode.value,
            'mouse_down_delay': self.mouse_down_delay,
            'pre_click_delay': self.pre_click_delay,
            'post_click_delay': self.post_click_delay,
            'continuous_clicks': self.continuous_clicks,
            'click_interval': self.click_interval,
            'hold_duration': self.hold_duration,
            'hold_clicks': self.hold_clicks,
            'move_steps': self.move_steps,
            'move_speed': self.move_speed,
            'jitter_range': self.jitter_range
        }
