#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证码检测服务
独立的后台验证码检测服务，使用多线程架构
"""

import asyncio
import threading
import time
from typing import Optional, Callable, Dict, Any
from ..utils.captcha_detector import CaptchaDetector
from ..utils.window_detector import WindowDetector
from ..utils.notification_system import NotificationSystem
from ..config.settings import Settings
from ..utils.logger import get_logger

class CaptchaService:
    """验证码检测服务"""
    
    def __init__(self):
        self.logger = get_logger()
        self.settings = Settings()
        self.window_detector = WindowDetector()
        self.captcha_detector = CaptchaDetector()
        self.notification_system = NotificationSystem()

        # 服务状态
        self.is_running = False
        self.is_enabled = False
        self.service_thread = None
        self.stop_event = threading.Event()

        # 配置参数
        self.target_hwnd = None
        self.detection_interval = 2.0
        self.auto_start = True

        # 回调函数
        self.captcha_detected_callback = None
        self.status_callback = None
        self.emergency_stop_callback = None

        # 统计信息
        self.detection_count = 0
        self.last_detection_time = 0
        self.service_start_time = 0

        # 初始化配置
        self._load_config()
        self._setup_callbacks()
        self._setup_notification_system()

    def _setup_notification_system(self):
        """设置通知系统"""
        try:
            # 从配置中加载通知设置
            captcha_settings = self.settings.get_captcha_settings()

            # 设置CAPTCHA检测的冷却期（从配置中读取，默认5分钟）
            cooldown_minutes = captcha_settings.get('alert_cooldown_minutes', 5)
            self.notification_system.set_cooldown_period("captcha_detected", cooldown_minutes * 60)

            # 设置声音和弹窗开关
            from ..utils.notification_system import NotificationType
            sound_enabled = captcha_settings.get('alert_sound_enabled', True)
            popup_enabled = captcha_settings.get('alert_popup_enabled', True)

            self.notification_system.enable_notification_type(NotificationType.SOUND, sound_enabled)
            self.notification_system.enable_notification_type(NotificationType.POPUP, popup_enabled)

            # 设置通知回调
            self.notification_system.set_notification_callback(
                "captcha_service",
                self._on_notification_callback
            )

            self._log_status(f"通知系统已初始化 - 冷却期: {cooldown_minutes}分钟, 声音: {sound_enabled}, 弹窗: {popup_enabled}")

        except Exception as e:
            self._log_status(f"通知系统初始化失败: {e}")

    def _on_notification_callback(self, notification_key: str, message: str):
        """通知回调处理"""
        self._log_status(f"收到通知: {notification_key} - {message}")

    def set_alert_cooldown(self, minutes: int):
        """设置警报冷却期（分钟）"""
        self.notification_system.set_cooldown_period("captcha_detected", minutes * 60)
        self._log_status(f"CAPTCHA警报冷却期已设置为: {minutes}分钟")

    def get_notification_status(self) -> dict:
        """获取通知状态"""
        return self.notification_system.get_cooldown_status()

    def set_sound_alert_enabled(self, enabled: bool):
        """设置声音警报开关"""
        from ..utils.notification_system import NotificationType
        self.notification_system.enable_notification_type(NotificationType.SOUND, enabled)
        self.settings.set_alert_sound_enabled(enabled)
        self._log_status(f"声音警报已{'启用' if enabled else '禁用'}")

    def set_popup_alert_enabled(self, enabled: bool):
        """设置弹窗警报开关"""
        from ..utils.notification_system import NotificationType
        self.notification_system.enable_notification_type(NotificationType.POPUP, enabled)
        self.settings.set_alert_popup_enabled(enabled)
        self._log_status(f"弹窗警报已{'启用' if enabled else '禁用'}")

    def update_alert_cooldown(self, minutes: int):
        """更新警报冷却期并保存到配置"""
        self.set_alert_cooldown(minutes)
        self.settings.set_alert_cooldown(minutes)
        self._log_status(f"警报冷却期已更新并保存: {minutes}分钟")
        
    def _load_config(self):
        """加载配置"""
        try:
            captcha_settings = self.settings.get_captcha_settings()
            
            self.is_enabled = captcha_settings.get('captcha_detection', False)
            template_path = captcha_settings.get('captcha_template', 'templates/captcha_default.png')
            threshold = captcha_settings.get('captcha_threshold', 0.8)
            self.detection_interval = captcha_settings.get('captcha_interval', 2.0)
            
            # 配置检测器
            self.captcha_detector.set_captcha_template(template_path)
            self.captcha_detector.match_threshold = threshold
            self.captcha_detector.detection_interval = self.detection_interval
            
            self._log_status(f"配置加载完成 - 启用: {self.is_enabled}, 模板: {template_path}")
            
        except Exception as e:
            self._log_status(f"配置加载失败: {e}")
            self.is_enabled = False
            
    def _setup_callbacks(self):
        """设置回调函数"""
        self.captcha_detector.set_status_callback(self._on_detector_status)
        self.captcha_detector.set_captcha_detected_callback(self._on_captcha_detected)
        
    def _log_status(self, message: str):
        """记录状态信息"""
        log_msg = f"[验证码服务] {message}"
        if self.status_callback:
            self.status_callback(log_msg)
        self.logger.main_logger.info(log_msg)
        
    def _on_detector_status(self, message: str):
        """检测器状态回调"""
        if self.status_callback:
            self.status_callback(message)
            
    def _on_captcha_detected(self):
        """验证码检测到时的回调"""
        self._log_status("🚨 验证码检测服务：发现验证码弹窗！")

        # 发送CAPTCHA警报通知
        alert_sent = self.notification_system.send_captcha_alert(
            "检测到验证码弹窗！自动点击已停止，请处理验证码后重新启动。"
        )

        if alert_sent:
            self._log_status("✅ CAPTCHA警报通知已发送")
        else:
            self._log_status("⏰ CAPTCHA警报在冷却期内，跳过通知")

        # 记录检测事件
        self.logger.log_emergency_stop("验证码检测服务", True)

        # 调用外部回调
        if self.captcha_detected_callback:
            try:
                self.captcha_detected_callback()
            except Exception as e:
                self._log_status(f"验证码回调执行失败: {e}")

        # 调用紧急停止回调
        if self.emergency_stop_callback:
            try:
                self.emergency_stop_callback("验证码检测")
            except Exception as e:
                self._log_status(f"紧急停止回调执行失败: {e}")
                
    def set_target_window(self, hwnd: int):
        """设置目标窗口"""
        self.target_hwnd = hwnd
        self.captcha_detector.set_target_window(hwnd)
        self._log_status(f"目标窗口已设置: {hwnd}")
        
    def set_captcha_detected_callback(self, callback: Callable[[], None]):
        """设置验证码检测回调"""
        self.captcha_detected_callback = callback
        
    def set_status_callback(self, callback: Callable[[str], None]):
        """设置状态回调"""
        self.status_callback = callback
        
    def set_emergency_stop_callback(self, callback: Callable[[str], None]):
        """设置紧急停止回调"""
        self.emergency_stop_callback = callback
        
    def auto_detect_game_window(self) -> bool:
        """自动检测游戏窗口"""
        try:
            self._log_status("正在自动检测游戏窗口...")
            
            windows = self.window_detector.get_all_windows()
            game_windows = []
            
            # 查找梦幻西游窗口
            for window in windows:
                title = window['title'].lower()
                if any(keyword in title for keyword in ['幽梦远', '梦幻', 'fantasy', 'westward']):
                    game_windows.append(window)
            
            if game_windows:
                # 使用第一个找到的窗口
                selected_window = game_windows[0]
                self.set_target_window(selected_window['hwnd'])
                self._log_status(f"自动选择窗口: {selected_window['title']}")
                return True
            else:
                self._log_status("未找到梦幻西游窗口")
                return False
                
        except Exception as e:
            self._log_status(f"自动检测窗口失败: {e}")
            return False
            
    def _service_loop(self):
        """服务主循环"""
        self._log_status("🔍 验证码检测服务开始运行")
        self.service_start_time = time.time()
        self.detection_count = 0
        
        while not self.stop_event.is_set():
            try:
                if not self.is_enabled:
                    # 如果未启用，等待一段时间后重新检查
                    if self.stop_event.wait(5.0):
                        break
                    continue
                    
                if not self.target_hwnd:
                    # 尝试自动检测窗口
                    if not self.auto_detect_game_window():
                        if self.stop_event.wait(10.0):  # 等待10秒后重试
                            break
                        continue
                
                # 执行检测
                self.detection_count += 1
                start_time = time.time()
                
                detected, x, y = self.captcha_detector.detect_captcha()
                detection_time = time.time() - start_time
                self.last_detection_time = time.time()
                
                # 记录检测日志（每20次记录一次正常检测）
                if self.detection_count % 20 == 0 or detected:
                    if detected:
                        self._log_status(f"🚨 第{self.detection_count}次检测: 发现验证码! 位置:({x},{y}) 耗时:{detection_time:.3f}s")
                    else:
                        runtime = time.time() - self.service_start_time
                        self._log_status(f"✅ 第{self.detection_count}次检测: 无验证码 耗时:{detection_time:.3f}s 运行:{runtime:.0f}s")
                
                # 等待下次检测
                if self.stop_event.wait(self.detection_interval):
                    break
                    
            except Exception as e:
                self._log_status(f"💥 服务循环异常: {e}")
                if self.stop_event.wait(5.0):  # 异常后等待5秒
                    break
                    
        runtime = time.time() - self.service_start_time
        self._log_status(f"🔍 验证码检测服务结束，运行{runtime:.0f}秒，共检测{self.detection_count}次")
        
    def start_service(self) -> bool:
        """启动验证码检测服务"""
        if self.is_running:
            self._log_status("验证码检测服务已在运行")
            return True
            
        try:
            self.stop_event.clear()
            self.service_thread = threading.Thread(target=self._service_loop, daemon=True, name="CaptchaService")
            self.service_thread.start()
            self.is_running = True
            
            self._log_status("🚀 验证码检测服务已启动")
            return True
            
        except Exception as e:
            self._log_status(f"启动验证码检测服务失败: {e}")
            return False
            
    def stop_service(self):
        """停止验证码检测服务"""
        if not self.is_running:
            return
            
        self._log_status("正在停止验证码检测服务...")
        self.stop_event.set()
        
        if self.service_thread and self.service_thread.is_alive():
            self.service_thread.join(timeout=5)
            
        self.is_running = False
        self._log_status("🛑 验证码检测服务已停止")
        
    def enable_detection(self, enabled: bool = True):
        """启用/禁用检测"""
        self.is_enabled = enabled
        if enabled:
            self._log_status("✅ 验证码检测已启用")
        else:
            self._log_status("⏸️ 验证码检测已禁用")
            
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        runtime = time.time() - self.service_start_time if self.service_start_time > 0 else 0
        
        return {
            "is_running": self.is_running,
            "is_enabled": self.is_enabled,
            "target_hwnd": self.target_hwnd,
            "detection_count": self.detection_count,
            "detection_interval": self.detection_interval,
            "runtime_seconds": runtime,
            "last_detection_time": self.last_detection_time,
            "thread_alive": self.service_thread.is_alive() if self.service_thread else False
        }
        
    def reload_config(self):
        """重新加载配置"""
        self._log_status("重新加载验证码检测配置...")
        self._load_config()
        
    def enable_debug_mode(self, enabled: bool = True, save_images: bool = False):
        """启用调试模式"""
        self.captcha_detector.enable_debug_mode(enabled, save_images)
        if enabled:
            self._log_status(f"🔧 验证码检测调试模式已启用，保存图像: {save_images}")
        else:
            self._log_status("🔧 验证码检测调试模式已禁用")
