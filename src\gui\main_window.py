#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI模块
梦幻西游自动走路工具的主界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from typing import Optional, Dict, Any

from ..utils.window_detector import WindowDetector
from ..utils.region_selector import RegionSelector
from ..core.auto_clicker import AutoClicker
from ..core.enhanced_mouse import ClickMode
from ..config.settings import Settings
from ..services.captcha_service import CaptchaService

class AutoWalkGUI:
    """自动走路工具主界面"""
    
    def __init__(self):
        self.root = None
        self.window_detector = WindowDetector()
        self.region_selector = RegionSelector()
        self.auto_clicker = AutoClicker()
        self.settings = Settings()
        self.captcha_service = CaptchaService()
        
        # 界面组件
        self.window_listbox = None
        self.region_label = None
        self.start_button = None
        self.stop_button = None
        self.status_text = None
        self.interval_min_var = None
        self.interval_max_var = None
        self.click_mode_var = None
        self.response_detection_var = None
        
        # 状态变量
        self.selected_window = None
        self.target_region = None
        
        # 设置自动点击器的状态回调
        self.auto_clicker.set_status_callback(self.update_status)

        # 设置验证码服务的回调
        self.captcha_service.set_status_callback(self.update_status)
        self.captcha_service.set_emergency_stop_callback(self.on_captcha_emergency_stop)
        
    def run(self):
        """启动GUI应用"""
        self.create_main_window()
        self.load_saved_settings()

        # 启动验证码检测服务
        self.start_captcha_service()

        # 设置程序退出时的清理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        self.root.mainloop()

    def start_captcha_service(self):
        """启动验证码检测服务"""
        try:
            if self.captcha_service.start_service():
                self.update_status("🚀 验证码检测服务已启动")
            else:
                self.update_status("⚠️ 验证码检测服务启动失败")
        except Exception as e:
            self.update_status(f"验证码服务启动异常: {e}")

    def on_captcha_emergency_stop(self, reason: str):
        """验证码紧急停止回调"""
        self.update_status(f"🚨 验证码触发紧急停止: {reason}")

        # 停止自动点击
        if hasattr(self, 'auto_clicker') and self.auto_clicker.is_running:
            self.auto_clicker.stop_clicking()
            self.start_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)

        # 显示提示对话框
        try:
            messagebox.showwarning(
                "验证码检测",
                f"检测到验证码弹窗！\n\n"
                f"原因: {reason}\n"
                f"自动点击已停止，请处理验证码后重新启动。"
            )
        except Exception as e:
            self.update_status(f"显示验证码提示失败: {e}")

    def on_closing(self):
        """程序关闭时的清理"""
        try:
            self.update_status("正在关闭程序...")

            # 停止自动点击
            if hasattr(self, 'auto_clicker'):
                self.auto_clicker.stop_clicking()

            # 停止验证码服务
            if hasattr(self, 'captcha_service'):
                self.captcha_service.stop_service()

            self.update_status("程序清理完成")

        except Exception as e:
            print(f"程序清理异常: {e}")
        finally:
            self.root.destroy()

    def create_main_window(self):
        """创建主窗口"""
        self.root = tk.Tk()
        self.root.title("梦幻西游自动走路工具 v1.1 - 增强版")
        self.root.geometry("750x700")
        self.root.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 创建各个部分
        self.create_window_selection_section(main_frame)
        self.create_region_selection_section(main_frame)
        self.create_settings_section(main_frame)
        self.create_click_mode_section(main_frame)
        self.create_control_section(main_frame)
        self.create_status_section(main_frame)
        
    def create_window_selection_section(self, parent):
        """创建窗口选择部分"""
        # 窗口选择标题
        ttk.Label(parent, text="1. 选择目标窗口:", font=("Arial", 10, "bold")).grid(
            row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 5)
        )
        
        # 刷新按钮
        ttk.Button(parent, text="刷新窗口列表", command=self.refresh_windows).grid(
            row=1, column=0, sticky=tk.W, padx=(0, 10)
        )
        
        # 窗口列表
        list_frame = ttk.Frame(parent)
        list_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 10))
        list_frame.columnconfigure(0, weight=1)
        
        # 创建列表框和滚动条
        self.window_listbox = tk.Listbox(list_frame, height=6)
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.window_listbox.yview)
        self.window_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.window_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 绑定选择事件
        self.window_listbox.bind('<<ListboxSelect>>', self.on_window_select)
        
    def create_region_selection_section(self, parent):
        """创建区域选择部分"""
        # 区域选择标题
        ttk.Label(parent, text="2. 选择点击区域:", font=("Arial", 10, "bold")).grid(
            row=3, column=0, columnspan=3, sticky=tk.W, pady=(10, 5)
        )
        
        # 选择区域按钮
        ttk.Button(parent, text="选择屏幕区域", command=self.select_region).grid(
            row=4, column=0, sticky=tk.W, padx=(0, 10)
        )
        
        # 区域信息显示
        self.region_label = ttk.Label(parent, text="未选择区域", foreground="red")
        self.region_label.grid(row=4, column=1, sticky=tk.W)
        
    def create_settings_section(self, parent):
        """创建设置部分"""
        # 设置标题
        ttk.Label(parent, text="3. 点击设置:", font=("Arial", 10, "bold")).grid(
            row=5, column=0, columnspan=3, sticky=tk.W, pady=(10, 5)
        )
        
        # 点击间隔设置
        settings_frame = ttk.Frame(parent)
        settings_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(settings_frame, text="点击间隔(秒):").grid(row=0, column=0, sticky=tk.W)
        
        ttk.Label(settings_frame, text="最小:").grid(row=0, column=1, sticky=tk.W, padx=(10, 5))
        self.interval_min_var = tk.StringVar(value="1.0")
        ttk.Entry(settings_frame, textvariable=self.interval_min_var, width=8).grid(row=0, column=2, sticky=tk.W)
        
        ttk.Label(settings_frame, text="最大:").grid(row=0, column=3, sticky=tk.W, padx=(10, 5))
        self.interval_max_var = tk.StringVar(value="3.0")
        ttk.Entry(settings_frame, textvariable=self.interval_max_var, width=8).grid(row=0, column=4, sticky=tk.W)

    def create_click_mode_section(self, parent):
        """创建点击模式选择部分"""
        # 点击模式标题
        ttk.Label(parent, text="4. 点击模式选择:", font=("Arial", 10, "bold")).grid(
            row=7, column=0, columnspan=3, sticky=tk.W, pady=(10, 5)
        )

        # 点击模式选择
        mode_frame = ttk.Frame(parent)
        mode_frame.grid(row=8, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        self.click_mode_var = tk.StringVar(value="normal")

        # 模式选项
        modes = [
            ("normal", "普通点击", "标准的单次点击，适合大多数情况"),
            ("continuous", "连续点击", "快速连续多次点击，适合需要持续移动的场景"),
            ("hold", "保持点击", "在一段时间内持续点击，模拟按住鼠标的效果"),
            ("smooth", "平滑移动", "先平滑移动鼠标再点击，最接近人类操作")
        ]

        for i, (value, text, desc) in enumerate(modes):
            rb = ttk.Radiobutton(
                mode_frame,
                text=text,
                variable=self.click_mode_var,
                value=value,
                command=self.on_click_mode_change
            )
            rb.grid(row=i//2, column=(i%2)*2, sticky=tk.W, padx=(0, 20), pady=2)

            # 添加描述标签
            desc_label = ttk.Label(mode_frame, text=f"({desc})", font=("Arial", 8), foreground="gray")
            desc_label.grid(row=i//2, column=(i%2)*2+1, sticky=tk.W, padx=(5, 20), pady=2)

        # 响应检测选项
        response_frame = ttk.Frame(mode_frame)
        response_frame.grid(row=2, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(10, 0))

        self.response_detection_var = tk.BooleanVar(value=False)
        response_cb = ttk.Checkbutton(
            response_frame,
            text="启用游戏响应检测 (实验性功能)",
            variable=self.response_detection_var,
            command=self.on_response_detection_change
        )
        response_cb.grid(row=0, column=0, sticky=tk.W)

        response_desc = ttk.Label(
            response_frame,
            text="检测游戏是否对点击产生响应，并自动调整点击策略",
            font=("Arial", 8),
            foreground="gray"
        )
        response_desc.grid(row=1, column=0, sticky=tk.W, pady=(2, 0))
        
    def create_control_section(self, parent):
        """创建控制部分"""
        # 控制标题
        ttk.Label(parent, text="5. 控制操作:", font=("Arial", 10, "bold")).grid(
            row=9, column=0, columnspan=3, sticky=tk.W, pady=(10, 5)
        )
        
        # 控制按钮
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=10, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.start_button = ttk.Button(control_frame, text="开始自动走路", command=self.start_auto_walk)
        self.start_button.grid(row=0, column=0, padx=(0, 10))

        self.stop_button = ttk.Button(control_frame, text="停止", command=self.stop_auto_walk, state=tk.DISABLED)
        self.stop_button.grid(row=0, column=1, padx=(0, 10))

        self.recover_button = ttk.Button(control_frame, text="紧急恢复", command=self.recover_from_emergency)
        self.recover_button.grid(row=0, column=2)
        
    def create_status_section(self, parent):
        """创建状态显示部分"""
        # 状态标题
        ttk.Label(parent, text="运行状态:", font=("Arial", 10, "bold")).grid(
            row=11, column=0, columnspan=3, sticky=tk.W, pady=(10, 5)
        )

        # 状态文本框
        self.status_text = scrolledtext.ScrolledText(parent, height=8, width=70)
        self.status_text.grid(row=12, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 配置网格权重
        parent.rowconfigure(12, weight=1)
        
        # 初始状态信息
        self.update_status("程序已启动，请选择目标窗口和区域")
        self.update_status("💡 提示：按ESC键可紧急停止程序运行")
        self.update_status("🔍 验证码检测服务正在初始化...")

    def on_click_mode_change(self):
        """点击模式变更处理"""
        mode_value = self.click_mode_var.get()
        try:
            click_mode = ClickMode(mode_value)
            self.auto_clicker.set_click_mode(click_mode)

            mode_names = {
                "normal": "普通点击",
                "continuous": "连续点击",
                "hold": "保持点击",
                "smooth": "平滑移动"
            }

            self.update_status(f"点击模式已切换为: {mode_names.get(mode_value, mode_value)}")

        except Exception as e:
            self.update_status(f"点击模式切换失败: {e}")

    def on_response_detection_change(self):
        """响应检测选项变更处理"""
        enabled = self.response_detection_var.get()
        self.auto_clicker.enable_response_detection(enabled)

        if enabled:
            self.update_status("游戏响应检测已启用")
        else:
            self.update_status("游戏响应检测已禁用")
        
    def refresh_windows(self):
        """刷新窗口列表"""
        self.window_listbox.delete(0, tk.END)
        windows = self.window_detector.get_all_windows()
        
        for window in windows:
            display_text = f"{window['title']} ({window['width']}x{window['height']})"
            self.window_listbox.insert(tk.END, display_text)
            
        self.update_status(f"已刷新窗口列表，找到 {len(windows)} 个窗口")
        
    def on_window_select(self, event):
        """窗口选择事件"""
        selection = self.window_listbox.curselection()
        if selection:
            index = selection[0]
            windows = self.window_detector.get_all_windows()
            if index < len(windows):
                self.selected_window = windows[index]
                self.update_status(f"已选择窗口: {self.selected_window['title']}")

                # 同时设置验证码服务的目标窗口
                if hasattr(self, 'captcha_service'):
                    self.captcha_service.set_target_window(self.selected_window['hwnd'])
                
    def select_region(self):
        """选择屏幕区域"""
        if not self.selected_window:
            messagebox.showwarning("警告", "请先选择目标窗口")
            return
            
        self.update_status("请在屏幕上拖拽选择区域...")
        
        # 隐藏主窗口
        self.root.withdraw()
        
        try:
            # 启动区域选择
            region = self.region_selector.select_region()
            
            if region:
                self.target_region = region
                region_text = f"区域: ({region[0]}, {region[1]}) - ({region[2]}, {region[3]})"
                self.region_label.config(text=region_text, foreground="green")
                self.update_status(f"已选择区域: {region_text}")
            else:
                self.update_status("区域选择已取消")
                
        except Exception as e:
            self.update_status(f"区域选择失败: {e}")
            messagebox.showerror("错误", f"区域选择失败: {e}")
        finally:
            # 恢复主窗口
            self.root.deiconify()
            self.root.lift()
            
    def start_auto_walk(self):
        """开始自动走路"""
        # 检查是否处于紧急停止状态
        if self.auto_clicker.is_emergency_stopped():
            result = messagebox.askyesno(
                "紧急停止状态",
                "检测到程序处于紧急停止状态。\n是否要先执行恢复操作？"
            )
            if result:
                if not self.recover_from_emergency():
                    return
            else:
                return

        # 验证设置
        if not self.selected_window:
            messagebox.showwarning("警告", "请先选择目标窗口")
            return

        if not self.target_region:
            messagebox.showwarning("警告", "请先选择点击区域")
            return
            
        try:
            # 获取点击间隔设置
            min_interval = float(self.interval_min_var.get())
            max_interval = float(self.interval_max_var.get())
            
            if min_interval <= 0 or max_interval <= 0 or min_interval > max_interval:
                messagebox.showerror("错误", "点击间隔设置无效")
                return
                
        except ValueError:
            messagebox.showerror("错误", "点击间隔必须是有效的数字")
            return
            
        # 配置自动点击器
        self.auto_clicker.set_target_region(self.target_region, self.selected_window['hwnd'])
        self.auto_clicker.set_click_interval(min_interval, max_interval)
        
        # 开始点击
        if self.auto_clicker.start_clicking():
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.save_current_settings()
        else:
            messagebox.showerror("错误", "启动自动点击失败")
            
    def stop_auto_walk(self):
        """停止自动走路"""
        self.auto_clicker.stop_clicking()
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        
    def update_status(self, message: str):
        """更新状态显示"""
        if self.status_text:
            timestamp = time.strftime("%H:%M:%S")
            self.status_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.status_text.see(tk.END)
            
    def load_saved_settings(self):
        """加载保存的设置"""
        # 加载点击间隔
        min_interval, max_interval = self.settings.get_click_interval()
        self.interval_min_var.set(str(min_interval))
        self.interval_max_var.set(str(max_interval))

        # 加载点击模式
        saved_mode = self.settings.get_click_mode()
        self.click_mode_var.set(saved_mode)
        self.on_click_mode_change()  # 应用模式设置

        # 加载响应检测设置
        response_detection = self.settings.get_response_detection()
        self.response_detection_var.set(response_detection)
        self.on_response_detection_change()  # 应用设置

        # 加载目标区域
        saved_region = self.settings.get_target_region()
        if saved_region:
            self.target_region = saved_region
            region_text = f"区域: ({saved_region[0]}, {saved_region[1]}) - ({saved_region[2]}, {saved_region[3]})"
            self.region_label.config(text=region_text, foreground="green")

        # 刷新窗口列表
        self.refresh_windows()
        
    def save_current_settings(self):
        """保存当前设置"""
        try:
            # 保存点击间隔
            min_interval = float(self.interval_min_var.get())
            max_interval = float(self.interval_max_var.get())
            self.settings.set_click_interval(min_interval, max_interval)

            # 保存点击模式
            self.settings.set_click_mode(self.click_mode_var.get())

            # 保存响应检测设置
            self.settings.set_response_detection(self.response_detection_var.get())

            # 保存目标区域
            if self.target_region:
                self.settings.set_target_region(self.target_region)

            # 保存窗口信息
            if self.selected_window:
                self.settings.set_last_window_info(
                    self.selected_window['title'],
                    self.selected_window['hwnd']
                )

        except Exception as e:
            self.update_status(f"保存设置失败: {e}")

    def recover_from_emergency(self) -> bool:
        """从紧急停止状态恢复"""
        try:
            self.update_status("正在执行紧急恢复...")

            # 执行恢复操作
            success = self.auto_clicker.recover_from_emergency_stop()

            if success:
                self.update_status("✅ 紧急恢复成功！程序已重置，可以重新启动")

                # 重置按钮状态
                self.start_button.config(state=tk.NORMAL)
                self.stop_button.config(state=tk.DISABLED)

                messagebox.showinfo("恢复成功", "程序已从紧急停止状态恢复，可以重新使用")
                return True
            else:
                self.update_status("❌ 紧急恢复失败，请重启程序")
                messagebox.showerror("恢复失败", "无法从紧急停止状态恢复，建议重启程序")
                return False

        except Exception as e:
            self.update_status(f"紧急恢复过程中发生错误: {e}")
            messagebox.showerror("恢复错误", f"恢复过程中发生错误: {e}")
            return False
