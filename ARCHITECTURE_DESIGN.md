# 自动走路工具 - 代码架构设计文档

## 项目概述

本项目是一个梦幻西游自动走路工具，具备智能CAPTCHA检测和警报系统。经过重构后，采用了模块化设计，消除了代码重复，提升了可维护性。

## 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                        GUI Layer                            │
│                   (src/gui/main_window.py)                  │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                     Service Layer                           │
│  ┌─────────────────┐    ┌─────────────────────────────────┐ │
│  │  CaptchaService │    │        AutoClicker              │ │
│  │   (检测服务)     │◄──►│      (点击控制)                  │ │
│  └─────────────────┘    └─────────────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                     Utils Layer                             │
│ ┌──────────────┐ ┌──────────────┐ ┌─────────────────────┐   │
│ │CaptchaDetector│ │Notification  │ │   Other Utils       │   │
│ │   (检测器)    │ │   System     │ │ (窗口管理/日志等)     │   │
│ │              │ │  (通知系统)   │ │                     │   │
│ └──────────────┘ └──────────────┘ └─────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件详解

### 1. GUI层 (src/gui/main_window.py)

**职责**: 用户界面管理和事件协调
- 管理主界面和用户交互
- 协调CaptchaService和AutoClicker
- 处理用户配置和状态显示

**关键方法**:
```python
def start_captcha_service()           # 启动CAPTCHA检测服务
def on_captcha_emergency_stop()      # 处理CAPTCHA紧急停止
def on_closing()                     # 程序退出清理
```

### 2. 服务层

#### CaptchaService (src/services/captcha_service.py)

**职责**: CAPTCHA检测服务的统一管理
- 管理CAPTCHA检测生命周期
- 集成通知系统
- 提供配置管理接口

**核心流程**:
```python
启动服务 → 自动检测游戏窗口 → 循环检测CAPTCHA → 发现CAPTCHA → 发送警报 → 触发回调
```

**关键方法**:
```python
def start_service()                  # 启动检测服务
def _service_loop()                  # 主检测循环
def _on_captcha_detected()           # CAPTCHA检测回调
def update_alert_cooldown()          # 更新警报冷却期
def set_sound_alert_enabled()        # 设置声音警报
def set_popup_alert_enabled()        # 设置弹窗警报
```

#### AutoClicker (src/core/auto_clicker.py)

**职责**: 自动点击功能管理
- 管理点击循环和逻辑
- 接收CAPTCHA检测结果
- 处理安全停止机制

**核心流程**:
```python
设置目标区域 → 开始点击循环 → 检查安全状态 → 执行点击 → 检测响应 → 继续循环
```

**关键方法**:
```python
def start_clicking()                 # 开始自动点击
def stop_clicking()                  # 停止自动点击
def on_captcha_detected()            # 接收CAPTCHA检测结果
def set_captcha_detected_callback()  # 设置CAPTCHA回调
```

### 3. 工具层

#### NotificationSystem (src/utils/notification_system.py)

**职责**: 统一的通知管理系统
- 支持多种通知方式(声音/弹窗/日志/回调)
- 实现智能冷却机制
- 提供灵活的配置选项

**通知类型**:
```python
class NotificationType(Enum):
    SOUND = "sound"        # 声音通知
    POPUP = "popup"        # 弹窗通知  
    LOG = "log"            # 日志通知
    CALLBACK = "callback"  # 回调通知
```

**核心方法**:
```python
def send_notification()              # 发送通知(通用)
def send_captcha_alert()             # 发送CAPTCHA警报
def set_cooldown_period()            # 设置冷却期
def get_cooldown_status()            # 获取冷却状态
```

#### CaptchaDetector (src/utils/captcha_detector.py)

**职责**: 底层CAPTCHA检测实现
- 窗口截图和图像处理
- 模板匹配算法
- 检测结果回调

**检测流程**:
```python
截取窗口 → 加载模板 → 图像预处理 → 模板匹配 → 计算相似度 → 返回结果
```

## 数据流向图

```
用户操作 → GUI → CaptchaService → CaptchaDetector → 检测结果
                     ↓
              NotificationSystem → 多种通知方式
                     ↓
               AutoClicker → 停止点击/安全处理
```

## 配置系统 (src/config/settings.py)

**CAPTCHA相关配置**:
```ini
[CAPTCHA]
captcha_detection = true              # 是否启用检测
captcha_template = templates/captcha_default.png  # 模板路径
captcha_threshold = 0.8               # 匹配阈值
captcha_interval = 2.0                # 检测间隔
alert_cooldown_minutes = 5            # 警报冷却期(分钟)
alert_sound_enabled = true            # 声音警报开关
alert_popup_enabled = true            # 弹窗警报开关
```

**配置管理方法**:
```python
def get_captcha_settings()            # 获取CAPTCHA配置
def set_alert_cooldown()              # 设置警报冷却期
def set_alert_sound_enabled()         # 设置声音警报
def set_alert_popup_enabled()         # 设置弹窗警报
```

## 主要执行流程

### 1. 程序启动流程
```
main.py → 检查权限 → 创建GUI → 加载配置 → 启动CAPTCHA服务 → 进入主循环
```

### 2. CAPTCHA检测流程
```
CaptchaService启动 → 检测游戏窗口 → 开始检测循环 → 发现CAPTCHA → 
发送通知 → 触发AutoClicker停止 → 显示用户警告
```

### 3. 通知系统流程
```
接收通知请求 → 检查冷却期 → 更新时间戳 → 并行发送多种通知 → 
记录日志 → 执行回调
```

## 关键设计模式

### 1. 观察者模式
- CaptchaService通过回调通知AutoClicker
- NotificationSystem通过回调通知外部组件

### 2. 策略模式  
- NotificationSystem支持多种通知策略
- 可以灵活启用/禁用不同通知方式

### 3. 单例模式
- Logger系统采用单例设计
- 配置系统保证全局一致性

## 错误处理机制

### 1. 异常捕获
- 所有关键操作都有try-catch保护
- 提供降级处理方案

### 2. 日志记录
- 详细的错误日志和操作日志
- 分级日志记录(INFO/WARNING/ERROR)

### 3. 状态恢复
- 组件失败时自动重试
- 保持系统整体稳定性

## 性能优化

### 1. 线程设计
- CAPTCHA检测在独立线程运行
- 通知发送异步执行，避免阻塞

### 2. 资源管理
- 及时释放图像处理资源
- 避免内存泄漏

### 3. 检测优化
- 可配置的检测间隔
- 智能的窗口检测机制

## 扩展性设计

### 1. 通知系统扩展
- 易于添加新的通知方式(邮件/短信等)
- 支持自定义通知模板

### 2. 检测算法扩展
- 支持多种CAPTCHA检测算法
- 可配置的检测参数

### 3. 配置系统扩展
- 支持动态配置加载
- 易于添加新的配置项

## 故障排查指南

### 1. CAPTCHA检测不工作
- 检查: 模板文件是否存在
- 检查: 目标窗口是否正确
- 检查: 检测阈值是否合适
- 日志: 查看captcha_detector相关日志

### 2. 通知不发送
- 检查: 通知类型是否启用
- 检查: 是否在冷却期内
- 检查: 配置文件是否正确
- 日志: 查看notification_system相关日志

### 3. 程序崩溃
- 检查: 权限是否足够
- 检查: 依赖库是否完整
- 日志: 查看error.log错误日志

这个架构设计确保了代码的可维护性、可扩展性和稳定性，为后续功能开发提供了坚实的基础。
