#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
区域选择工具模块
用于让用户选择屏幕区域
"""

import tkinter as tk
from tkinter import messagebox
from typing import Tuple, Optional, Callable
import time

class RegionSelector:
    """屏幕区域选择器"""
    
    def __init__(self):
        self.start_x = 0
        self.start_y = 0
        self.end_x = 0
        self.end_y = 0
        self.selecting = False
        self.selection_complete = False
        self.callback = None
        
    def select_region(self, callback: Optional[Callable] = None) -> Optional[Tuple[int, int, int, int]]:
        """
        启动区域选择
        返回: (left, top, right, bottom) 或 None
        """
        self.callback = callback
        self.selection_complete = False
        
        # 创建全屏透明窗口
        self.root = tk.Tk()
        self.root.attributes('-fullscreen', True)
        self.root.attributes('-alpha', 0.3)
        self.root.attributes('-topmost', True)
        self.root.configure(bg='gray')
        
        # 创建画布
        self.canvas = tk.Canvas(
            self.root, 
            highlightthickness=0,
            bg='gray'
        )
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定鼠标事件
        self.canvas.bind('<Button-1>', self._on_mouse_down)
        self.canvas.bind('<B1-Motion>', self._on_mouse_drag)
        self.canvas.bind('<ButtonRelease-1>', self._on_mouse_up)
        self.canvas.bind('<Escape>', self._on_escape)
        
        # 绑定键盘事件
        self.root.bind('<Escape>', self._on_escape)
        self.root.focus_set()
        
        # 添加提示文本
        self.canvas.create_text(
            self.root.winfo_screenwidth() // 2,
            50,
            text="拖拽鼠标选择区域，按ESC取消",
            fill="white",
            font=("Arial", 16)
        )
        
        # 运行选择界面
        self.root.mainloop()
        
        if self.selection_complete:
            return (
                min(self.start_x, self.end_x),
                min(self.start_y, self.end_y),
                max(self.start_x, self.end_x),
                max(self.start_y, self.end_y)
            )
        return None
    
    def _on_mouse_down(self, event):
        """鼠标按下事件"""
        self.start_x = event.x_root
        self.start_y = event.y_root
        self.selecting = True
        
    def _on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if self.selecting:
            self.end_x = event.x_root
            self.end_y = event.y_root
            
            # 清除之前的选择框
            self.canvas.delete("selection")
            
            # 绘制新的选择框
            self.canvas.create_rectangle(
                self.start_x, self.start_y,
                self.end_x, self.end_y,
                outline="red",
                width=2,
                tags="selection"
            )
    
    def _on_mouse_up(self, event):
        """鼠标释放事件"""
        if self.selecting:
            self.end_x = event.x_root
            self.end_y = event.y_root
            self.selecting = False
            self.selection_complete = True
            
            # 确认选择
            if abs(self.end_x - self.start_x) > 10 and abs(self.end_y - self.start_y) > 10:
                self.root.quit()
                self.root.destroy()
            else:
                messagebox.showwarning("警告", "选择区域太小，请重新选择")
                self.canvas.delete("selection")
    
    def _on_escape(self, event):
        """ESC键取消选择"""
        self.selection_complete = False
        self.root.quit()
        self.root.destroy()
