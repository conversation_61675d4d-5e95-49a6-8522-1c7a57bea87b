#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的验证码调试脚本
"""

import os
import time
import cv2

def debug_captcha():
    """调试验证码检测"""
    print("🔬 开始验证码调试...")
    
    try:
        from src.utils.captcha_detector import CaptchaDetector
        from src.utils.window_detector import WindowDetector
        from src.config.settings import Settings
        
        # 创建实例
        detector = CaptchaDetector()
        window_detector = WindowDetector()
        settings = Settings()
        
        print("✅ 模块导入成功")
        
        # 查找游戏窗口
        print("🔍 查找游戏窗口...")
        windows = window_detector.get_all_windows()
        game_windows = []
        
        for window in windows:
            title = window['title'].lower()
            # 排除工具自身的窗口，只匹配真正的游戏窗口
            if any(keyword in title for keyword in ['幽梦远']) and '自动走路工具' not in title:
                game_windows.append(window)
        
        if not game_windows:
            print("❌ 未找到游戏窗口")
            return
            
        print(f"找到 {len(game_windows)} 个游戏窗口:")
        for i, window in enumerate(game_windows):
            print(f"  {i+1}. {window['title']}")
            
        # 使用第一个窗口
        target_window = game_windows[0]
        print(f"使用窗口: {target_window['title']}")
        
        # 设置目标窗口
        detector.set_target_window(target_window['hwnd'])
        
        # 获取配置
        captcha_settings = settings.get_captcha_settings()
        template_path = captcha_settings.get('captcha_template', 'templates/captcha_default.png')
        threshold = captcha_settings.get('captcha_threshold', 0.8)
        
        print(f"模板路径: {template_path}")
        print(f"匹配阈值: {threshold}")
        
        # 检查模板文件
        if not os.path.exists(template_path):
            print(f"❌ 模板文件不存在: {template_path}")
            return
            
        # 设置模板
        detector.set_captcha_template(template_path)
        
        # 创建调试目录
        debug_dir = "captcha_debug"
        if not os.path.exists(debug_dir):
            os.makedirs(debug_dir)
            
        # 截取当前窗口
        print("📸 截取窗口...")
        image = detector._capture_window(target_window['hwnd'])
        
        if image is None:
            print("❌ 窗口截图失败")
            return
            
        print(f"✅ 截图成功，尺寸: {image.shape}")
        
        # 保存截图
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        screenshot_path = f"{debug_dir}/current_screenshot_{timestamp}.png"
        
        if len(image.shape) == 3 and image.shape[2] == 4:  # BGRA
            image_bgr = cv2.cvtColor(image, cv2.COLOR_BGRA2BGR)
        else:
            image_bgr = image
            
        cv2.imwrite(screenshot_path, image_bgr)
        print(f"📁 截图已保存: {screenshot_path}")
        
        # 执行检测
        print("🎯 执行验证码检测...")
        detected, x, y = detector.detect_captcha()
        
        print(f"检测结果: {'发现验证码' if detected else '未发现验证码'}")
        if detected:
            print(f"位置: ({x}, {y})")
        
        # 测试不同阈值
        print("\n🔧 测试不同阈值...")
        original_threshold = detector.match_threshold
        
        thresholds = [0.3, 0.4, 0.5, 0.6, 0.7, 0.8]
        for test_threshold in thresholds:
            detector.match_threshold = test_threshold
            detected, x, y = detector.detect_captcha()
            status = "✅ 检测到" if detected else "❌ 未检测到"
            print(f"  阈值 {test_threshold}: {status}")
            if detected:
                print(f"    位置: ({x}, {y})")
        
        # 恢复原始阈值
        detector.match_threshold = original_threshold
        
        # 详细分析模板匹配
        print("\n🔍 详细模板匹配分析...")
        
        # 读取模板
        template = cv2.imread(template_path, cv2.IMREAD_UNCHANGED)
        if template is None:
            print("❌ 无法读取模板")
            return
            
        print(f"模板尺寸: {template.shape}")
        
        # 转换为灰度图
        if len(template.shape) == 3:
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
        else:
            template_gray = template
            
        image_gray = cv2.cvtColor(image, cv2.COLOR_BGRA2GRAY)
        
        # 执行模板匹配
        result = cv2.matchTemplate(image_gray, template_gray, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        
        print(f"匹配结果详情:")
        print(f"  最大匹配值: {max_val:.6f}")
        print(f"  最大匹配位置: {max_loc}")
        print(f"  当前阈值: {original_threshold}")
        print(f"  是否超过阈值: {'是' if max_val >= original_threshold else '否'}")
        
        # 保存匹配结果可视化
        h, w = template_gray.shape[:2]
        top_left = max_loc
        bottom_right = (top_left[0] + w, top_left[1] + h)
        
        marked_image = cv2.cvtColor(image_gray, cv2.COLOR_GRAY2BGR)
        cv2.rectangle(marked_image, top_left, bottom_right, (0, 255, 0), 2)
        cv2.putText(marked_image, f"Match: {max_val:.3f}", 
                   (top_left[0], top_left[1] - 10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        match_result_path = f"{debug_dir}/match_result_{timestamp}.png"
        cv2.imwrite(match_result_path, marked_image)
        print(f"📁 匹配结果已保存: {match_result_path}")
        
        # 保存模板用于对比
        template_path_debug = f"{debug_dir}/template_{timestamp}.png"
        cv2.imwrite(template_path_debug, template_gray)
        print(f"📁 模板已保存: {template_path_debug}")
        
        print(f"\n📊 调试总结:")
        if max_val >= original_threshold:
            print(f"✅ 验证码检测应该成功")
        else:
            print(f"❌ 验证码检测失败")
            print(f"   当前匹配值 {max_val:.3f} < 阈值 {original_threshold}")
            print(f"💡 建议:")
            if max_val > 0.3:
                print(f"   - 降低阈值到 {max_val + 0.1:.1f}")
            else:
                print(f"   - 重新制作验证码模板")
                print(f"   - 确认验证码确实存在于截图中")
        
        print(f"\n📁 所有调试文件保存在: {debug_dir}/")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_captcha()
