#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证码检测状态监控工具
实时监控验证码检测的运行状态和日志输出
"""

import time
import threading
from src.utils.captcha_detector import CaptchaDetector
from src.utils.window_detector import WindowDetector
from src.config.settings import Settings

class CaptchaMonitor:
    """验证码检测监控器"""
    
    def __init__(self):
        self.detector = CaptchaDetector()
        self.window_detector = WindowDetector()
        self.settings = Settings()
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 设置状态回调
        self.detector.set_status_callback(self.on_status_update)
        
        # 启用调试模式
        self.detector._debug_mode = True
        
    def on_status_update(self, message: str):
        """状态更新回调"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def find_game_window(self):
        """查找梦幻西游窗口"""
        print("🔍 正在查找梦幻西游窗口...")
        
        windows = self.window_detector.get_all_windows()
        game_windows = []
        
        for window in windows:
            title = window['title'].lower()
            if any(keyword in title for keyword in ['幽梦远']):
                game_windows.append(window)
        
        if game_windows:
            print(f"找到 {len(game_windows)} 个梦幻西游窗口:")
            for i, window in enumerate(game_windows):
                print(f"  {i+1}. {window['title']} (句柄: {window['hwnd']})")
            
            # 使用第一个窗口
            selected_window = game_windows[0]
            print(f"✅ 选择窗口: {selected_window['title']}")
            return selected_window['hwnd']
        else:
            print("❌ 未找到梦幻西游窗口")
            return None
            
    def setup_captcha_detection(self):
        """设置验证码检测"""
        print("\n🔧 设置验证码检测...")
        
        # 获取配置
        captcha_settings = self.settings.get_captcha_settings()
        print(f"配置信息: {captcha_settings}")
        
        # 查找游戏窗口
        hwnd = self.find_game_window()
        if not hwnd:
            return False
            
        # 设置目标窗口
        self.detector.set_target_window(hwnd)
        
        # 设置模板路径
        template_path = captcha_settings.get('captcha_template', 'templates/captcha_default.png')
        self.detector.set_captcha_template(template_path)
        
        # 设置检测参数
        self.detector.match_threshold = captcha_settings.get('captcha_threshold', 0.8)
        self.detector.detection_interval = captcha_settings.get('captcha_interval', 2.0)
        
        print(f"✅ 验证码检测设置完成")
        print(f"   目标窗口: {hwnd}")
        print(f"   模板路径: {template_path}")
        print(f"   匹配阈值: {self.detector.match_threshold}")
        print(f"   检测间隔: {self.detector.detection_interval}秒")
        
        return True
        
    def test_single_detection(self):
        """测试单次检测"""
        print("\n🎯 执行单次验证码检测...")
        
        start_time = time.time()
        detected, x, y = self.detector.detect_captcha()
        detection_time = time.time() - start_time
        
        if detected:
            print(f"🚨 检测到验证码! 位置: ({x}, {y}), 耗时: {detection_time:.3f}秒")
        else:
            print(f"✅ 未检测到验证码, 耗时: {detection_time:.3f}秒")
            
        return detected
        
    def start_continuous_monitoring(self):
        """开始连续监控"""
        print("\n🔄 开始连续验证码监控...")
        print("按 Ctrl+C 停止监控")
        
        if self.detector.start_monitoring():
            self.is_monitoring = True
            print("✅ 验证码监控已启动")
            
            try:
                # 保持监控运行
                while self.is_monitoring:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n⏹️ 用户中断监控")
                self.stop_monitoring()
        else:
            print("❌ 验证码监控启动失败")
            
    def stop_monitoring(self):
        """停止监控"""
        if self.is_monitoring:
            self.detector.stop_monitoring()
            self.is_monitoring = False
            print("🛑 验证码监控已停止")
            
    def show_status(self):
        """显示当前状态"""
        status = self.detector.get_status()
        print("\n📊 验证码检测器状态:")
        print(f"   监控状态: {'运行中' if status['is_monitoring'] else '已停止'}")
        print(f"   目标窗口: {status['target_hwnd']}")
        print(f"   模板路径: {status['template_path']}")
        print(f"   检测间隔: {status['detection_interval']}秒")
        print(f"   匹配阈值: {status['match_threshold']}")
        print(f"   上次检测: {time.ctime(status['last_detection_time']) if status['last_detection_time'] > 0 else '从未检测'}")
        
def show_menu():
    """显示菜单"""
    print("\n" + "="*50)
    print("🔍 验证码检测监控工具")
    print("="*50)
    print("1. 设置验证码检测")
    print("2. 执行单次检测")
    print("3. 开始连续监控")
    print("4. 显示当前状态")
    print("5. 停止监控")
    print("0. 退出")
    print("="*50)

def main():
    """主函数"""
    monitor = CaptchaMonitor()
    
    print("🎮 梦幻西游验证码检测监控工具")
    print("用于实时监控验证码检测功能的运行状态")
    
    while True:
        show_menu()
        
        try:
            choice = input("请选择操作 (0-5): ").strip()
            
            if choice == '0':
                print("👋 退出监控工具")
                monitor.stop_monitoring()
                break
            elif choice == '1':
                monitor.setup_captcha_detection()
            elif choice == '2':
                monitor.test_single_detection()
            elif choice == '3':
                monitor.start_continuous_monitoring()
            elif choice == '4':
                monitor.show_status()
            elif choice == '5':
                monitor.stop_monitoring()
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            monitor.stop_monitoring()
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")

if __name__ == "__main__":
    main()
