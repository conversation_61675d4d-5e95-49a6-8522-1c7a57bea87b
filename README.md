# 梦幻西游自动走路工具 v1.1 增强版

一个专为梦幻西游MMORPG游戏设计的自动走路辅助工具，通过高度优化的鼠标点击模拟实现角色自动移动，解放玩家双手。

## 🎯 功能特性

### 核心功能（已实现）
- ✅ **窗口检测与选择**：自动检测系统中的所有窗口，支持选择目标游戏窗口
- ✅ **区域选择**：可视化选择屏幕区域，精确定义点击范围
- ✅ **增强鼠标模拟**：高度优化的鼠标操作，更接近真实人类行为
- ✅ **多种点击模式**：普通点击、连续点击、保持点击、平滑移动四种模式
- ✅ **智能响应检测**：实验性功能，检测游戏响应并自动调整策略
- ✅ **安全机制**：多重安全检查，确保只在指定窗口内操作
- ✅ **配置管理**：自动保存和加载用户设置
- ✅ **友好界面**：简洁直观的GUI界面，易于操作

### 🆕 v1.1 增强功能
- 🔥 **真实鼠标模拟**：模拟人类鼠标按下/弹起延迟（50-150ms随机）
- 🔥 **平滑移动轨迹**：贝塞尔曲线路径，自然抖动效果
- 🔥 **连续点击模式**：快速连续多次点击，保持角色移动状态
- 🔥 **保持点击模式**：在指定时间内持续点击，模拟按住效果
- 🔥 **游戏响应检测**：智能检测点击效果，自动优化点击策略
- 🔥 **点击前后停顿**：模拟人类反应时间，增加真实性

### 第二阶段功能（计划中）
- 🔄 **战斗检测**：自动检测战斗状态，战斗时暂停走路
- 🔄 **智能恢复**：战斗结束后自动恢复走路功能

## 🚀 快速开始

### 系统要求
- Windows 10/11 操作系统
- Python 3.7 或更高版本
- 梦幻西游客户端

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd auto_walk
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行程序**
   ```bash
   python main.py
   ```

## 📖 使用指南

### 基本使用流程

1. **启动程序**
   - 运行 `python main.py` 启动工具
   - 程序将显示主界面

2. **选择目标窗口**
   - 点击"刷新窗口列表"按钮
   - 从列表中选择梦幻西游游戏窗口

3. **选择点击区域**
   - 点击"选择屏幕区域"按钮
   - 在屏幕上拖拽选择游戏的可走动区域
   - 避免选择UI界面和不可点击的区域

4. **配置点击设置**
   - 设置最小和最大点击间隔（推荐1-3秒）
   - 间隔时间影响走路的频率

5. **选择点击模式**
   - **普通点击**：标准单次点击，适合大多数情况
   - **连续点击**：快速连续多次点击，适合需要持续移动
   - **保持点击**：在一段时间内持续点击，模拟按住效果
   - **平滑移动**：先平滑移动鼠标再点击，最接近人类操作
   - 可选启用游戏响应检测（实验性功能）

6. **开始自动走路**
   - 点击"开始自动走路"按钮
   - 程序将在指定区域内随机点击
   - 观察状态窗口了解运行情况

7. **停止操作**
   - 点击"停止"按钮正常停止
   - 或将鼠标移到屏幕左上角紧急停止
   - 或按下 Ctrl+Alt+S 组合键紧急停止

### 安全提示

⚠️ **重要安全功能**
- **紧急停止**：鼠标移到屏幕左上角可立即停止所有操作
- **快捷键停止**：Ctrl+Alt+S 组合键可紧急停止
- **窗口检测**：只在指定窗口内操作，窗口关闭时自动停止
- **频率限制**：内置点击频率限制，防止过度操作

## 🏗️ 项目架构

### 目录结构
```
auto_walk/
├── main.py                 # 程序入口
├── requirements.txt        # 依赖列表
├── config.ini             # 配置文件（自动生成）
├── README.md              # 项目文档
└── src/                   # 源代码目录
    ├── core/              # 核心功能模块
    │   └── auto_clicker.py    # 自动点击器
    ├── gui/               # 用户界面模块
    │   └── main_window.py     # 主窗口界面
    ├── utils/             # 工具模块
    │   ├── window_detector.py # 窗口检测器
    │   ├── region_selector.py # 区域选择器
    │   └── safety_checker.py  # 安全检查器
    └── config/            # 配置管理模块
        └── settings.py        # 设置管理器
```

### 核心模块说明

#### AutoClicker (自动点击器)
- 负责在指定区域内执行随机点击
- 集成安全检查机制
- 支持多线程运行，不阻塞界面

#### WindowDetector (窗口检测器)
- 检测系统中所有可见窗口
- 获取窗口位置和大小信息
- 验证窗口有效性

#### RegionSelector (区域选择器)
- 提供可视化区域选择界面
- 支持拖拽选择屏幕区域
- 实时显示选择框

#### SafetyChecker (安全检查器)
- 监控紧急停止条件
- 限制点击频率
- 验证操作安全性

#### Settings (设置管理器)
- 自动保存和加载配置
- 支持多种配置项
- INI格式配置文件

## ⚙️ 配置说明

程序会自动创建 `config.ini` 配置文件，主要配置项：

```ini
[WINDOW]
last_window_title = 梦幻西游    # 上次选择的窗口
target_region = 100,100,800,600 # 点击区域坐标

[CLICKING]
min_interval = 1.0              # 最小点击间隔
max_interval = 3.0              # 最大点击间隔
click_margin = 10               # 点击边距

[GUI]
window_width = 700              # 界面宽度
window_height = 600             # 界面高度
always_on_top = False           # 是否置顶
```

## 🔧 故障排除

### 常见问题

**Q: 程序无法检测到游戏窗口？**
A: 确保游戏正在运行且窗口可见，点击"刷新窗口列表"重新检测。

**Q: 点击位置不准确？**
A: 重新选择点击区域，确保选择的是游戏内可走动的区域。

**Q: 程序运行后没有反应？**
A: 检查是否正确选择了窗口和区域，查看状态窗口的提示信息。

**Q: 如何紧急停止程序？**
A: 将鼠标快速移动到屏幕左上角，或按下Ctrl+Alt+S组合键。

### 依赖问题

如果遇到依赖安装问题，可以尝试：
```bash
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall
```

## 🚧 未来扩展计划

### 第二阶段开发计划
1. **战斗检测系统**
   - 基于屏幕颜色变化检测战斗状态
   - 支持多种战斗界面识别
   - 智能暂停和恢复机制

2. **高级功能**
   - 路径记录和回放
   - 多点位循环走动
   - 自定义走路模式

3. **用户体验优化**
   - 热键支持
   - 系统托盘运行
   - 更丰富的配置选项

## 📄 许可证

本项目仅供学习和研究使用，请遵守游戏相关规定，合理使用辅助工具。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

---

**免责声明**：本工具仅用于学习和研究目的，使用者需自行承担使用风险，并遵守相关游戏规则。
