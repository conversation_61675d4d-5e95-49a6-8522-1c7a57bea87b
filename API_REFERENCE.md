# API参考文档

## 核心组件API

### CaptchaService API

#### 基本控制
```python
# 启动/停止服务
captcha_service.start_service() -> bool
captcha_service.stop_service() -> bool

# 设置目标窗口
captcha_service.set_target_window(hwnd: int)

# 自动检测游戏窗口
captcha_service.auto_detect_game_window() -> bool
```

#### 回调设置
```python
# 设置状态回调
captcha_service.set_status_callback(callback: Callable[[str], None])

# 设置CAPTCHA检测回调
captcha_service.set_captcha_detected_callback(callback: Callable[[], None])

# 设置紧急停止回调
captcha_service.set_emergency_stop_callback(callback: Callable[[str], None])
```

#### 通知配置
```python
# 更新警报冷却期
captcha_service.update_alert_cooldown(minutes: int)

# 设置声音警报
captcha_service.set_sound_alert_enabled(enabled: bool)

# 设置弹窗警报
captcha_service.set_popup_alert_enabled(enabled: bool)

# 获取通知状态
captcha_service.get_notification_status() -> dict
```

### NotificationSystem API

#### 基本通知
```python
# 发送通用通知
notification_system.send_notification(
    notification_key: str,      # 通知键(用于冷却控制)
    title: str,                 # 通知标题
    message: str,               # 通知消息
    level: str = "warning",     # 日志级别
    enable_sound: bool = True,  # 启用声音
    enable_popup: bool = True,  # 启用弹窗
    enable_log: bool = True,    # 启用日志
    enable_callback: bool = True, # 启用回调
    force: bool = False         # 强制发送(忽略冷却)
) -> bool

# 发送CAPTCHA专用警报
notification_system.send_captcha_alert(message: str = "检测到验证码弹窗！") -> bool

# 发送紧急停止警报
notification_system.send_emergency_stop_alert(reason: str) -> bool
```

#### 冷却管理
```python
# 设置冷却期
notification_system.set_cooldown_period(notification_key: str, seconds: int)

# 获取冷却状态
notification_system.get_cooldown_status() -> Dict[str, Dict[str, Any]]
```

#### 通知类型控制
```python
from src.utils.notification_system import NotificationType

# 启用/禁用通知类型
notification_system.enable_notification_type(
    notification_type: NotificationType, 
    enabled: bool = True
)

# 设置通知回调
notification_system.set_notification_callback(
    callback_name: str, 
    callback: Callable[[str, str], None]
)
```

### AutoClicker API

#### 基本控制
```python
# 开始/停止点击
auto_clicker.start_clicking()
auto_clicker.stop_clicking()

# 设置目标区域
auto_clicker.set_target_region(
    region: Tuple[int, int, int, int],  # (left, top, right, bottom)
    hwnd: Optional[int] = None          # 窗口句柄
)

# 设置点击间隔
auto_clicker.set_click_interval(min_interval: float, max_interval: float)
```

#### CAPTCHA集成
```python
# 设置CAPTCHA检测回调
auto_clicker.set_captcha_detected_callback(callback: Callable[[], None])

# CAPTCHA检测处理(由CaptchaService调用)
auto_clicker.on_captcha_detected()
```

#### 响应检测
```python
# 启用/禁用响应检测
auto_clicker.enable_response_detection(enabled: bool = True)
```

### Settings API

#### CAPTCHA配置
```python
# 获取CAPTCHA设置
settings.get_captcha_settings() -> dict

# 设置CAPTCHA检测开关
settings.set_captcha_detection(enabled: bool)

# 设置CAPTCHA模板
settings.set_captcha_template(template_path: str)

# 设置警报冷却期
settings.set_alert_cooldown(minutes: int)

# 设置声音警报
settings.set_alert_sound_enabled(enabled: bool)

# 设置弹窗警报
settings.set_alert_popup_enabled(enabled: bool)
```

#### 其他配置
```python
# 获取窗口设置
settings.get_window_settings() -> dict

# 获取点击设置
settings.get_clicking_settings() -> dict

# 获取GUI设置
settings.get_gui_settings() -> dict

# 保存设置
settings.save_settings()
```

## 配置文件格式

### config.ini 完整配置
```ini
[WINDOW]
last_window_title = 梦幻西游 聊天窗口
last_window_hwnd = 4261374
target_region = 195,128,852,699

[CLICKING]
min_interval = 0.5
max_interval = 1.5
click_margin = 10
click_mode = smooth
response_detection = false

[CAPTCHA]
captcha_detection = true
captcha_template = templates/captcha_default.png
captcha_threshold = 0.8
captcha_interval = 2.0
alert_cooldown_minutes = 5
alert_sound_enabled = true
alert_popup_enabled = true

[GUI]
window_width = 600
window_height = 500
always_on_top = false
```

## 事件回调接口

### 状态回调
```python
def status_callback(message: str) -> None:
    """状态更新回调"""
    print(f"状态: {message}")
```

### CAPTCHA检测回调
```python
def captcha_detected_callback() -> None:
    """CAPTCHA检测到时的回调"""
    print("检测到CAPTCHA，执行相应处理")
```

### 紧急停止回调
```python
def emergency_stop_callback(reason: str) -> None:
    """紧急停止回调"""
    print(f"紧急停止: {reason}")
```

### 通知回调
```python
def notification_callback(notification_key: str, message: str) -> None:
    """通知回调"""
    print(f"收到通知 {notification_key}: {message}")
```

## 数据结构

### 通知状态结构
```python
{
    "notification_key": {
        "cooldown_period": 300,        # 冷却期(秒)
        "last_notification": 1234567890, # 最后通知时间戳
        "remaining_cooldown": 120,     # 剩余冷却时间(秒)
        "is_in_cooldown": True         # 是否在冷却期
    }
}
```

### CAPTCHA设置结构
```python
{
    "captcha_detection": True,
    "captcha_template": "templates/captcha_default.png",
    "captcha_threshold": 0.8,
    "captcha_interval": 2.0,
    "alert_cooldown_minutes": 5,
    "alert_sound_enabled": True,
    "alert_popup_enabled": True
}
```

### 窗口信息结构
```python
{
    "hwnd": 4261374,
    "title": "梦幻西游 聊天窗口",
    "class_name": "Window Class",
    "rect": (100, 100, 800, 600)
}
```

## 使用示例

### 基本使用流程
```python
# 1. 创建组件
captcha_service = CaptchaService()
auto_clicker = AutoClicker()

# 2. 设置回调
captcha_service.set_captcha_detected_callback(auto_clicker.on_captcha_detected)
auto_clicker.set_captcha_detected_callback(lambda: print("CAPTCHA处理完成"))

# 3. 配置参数
captcha_service.update_alert_cooldown(10)  # 10分钟冷却
captcha_service.set_sound_alert_enabled(True)

# 4. 启动服务
captcha_service.start_service()

# 5. 设置点击区域并开始
auto_clicker.set_target_region((100, 100, 800, 600), hwnd)
auto_clicker.start_clicking()
```

### 自定义通知处理
```python
def custom_notification_handler(notification_key: str, message: str):
    if notification_key == "captcha_detected":
        # 自定义CAPTCHA处理逻辑
        send_email_alert(message)
        play_custom_sound()

# 注册自定义处理器
notification_system = NotificationSystem()
notification_system.set_notification_callback("custom", custom_notification_handler)
```

### 动态配置调整
```python
# 运行时调整配置
settings = Settings()

# 调整冷却期
settings.set_alert_cooldown(15)  # 15分钟

# 切换通知方式
settings.set_alert_sound_enabled(False)  # 禁用声音
settings.set_alert_popup_enabled(True)   # 启用弹窗

# 重新加载配置
captcha_service._setup_notification_system()
```

这个API参考文档提供了所有核心组件的详细接口说明，方便开发者理解和使用各个功能模块。
