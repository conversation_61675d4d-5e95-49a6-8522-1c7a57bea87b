#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
梦幻西游自动走路工具 - 主程序入口
Fantasy Westward Journey Auto Walk Tool - Main Entry Point

作者: Auto Walk Tool Developer
版本: 1.0.0
"""

import sys
import os
from ctypes import windll, byref
import tkinter as tk
from tkinter import messagebox

def check_admin_privileges():
    """检查管理员权限并询问用户"""
    try:
        is_admin = windll.shell32.IsUserAnAdmin()
        return is_admin
    except:
        return False

def ask_for_admin():
    """询问用户是否需要管理员权限"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    result = messagebox.askyesno(
        "权限提示",
        "检测到程序未以管理员权限运行。\n\n"
        "管理员权限可以提供更好的鼠标控制效果，\n"
        "但不是必需的（程序会自动使用兼容模式）。\n\n"
        "是否要以管理员权限重新启动程序？\n\n"
        "选择'否'将以当前权限继续运行。"
    )

    root.destroy()
    return result

def main():
    """主程序入口"""
    try:
        print("正在启动梦幻西游自动走路工具...")
        print("正在导入模块...")

        from src.gui.main_window import AutoWalkGUI
        print("模块导入成功！")

        # 创建并启动GUI应用
        print("正在启动GUI界面...")
        app = AutoWalkGUI()
        app.run()
    except ImportError as e:
        print(f"模块导入失败: {e}")
        print("请确保已安装所有依赖库：pip3 install -r requirements.txt")
        input("按回车键退出...")
        sys.exit(1)
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    # 检查是否已经是管理员权限
    if not check_admin_privileges():
        # 询问用户是否需要管理员权限
        if ask_for_admin():
            try:
                # 用户选择提权，重新启动程序
                windll.shell32.ShellExecuteW(None, "runas", sys.executable, __file__, None, 1)
                sys.exit(0)  # 退出当前进程
            except Exception as e:
                print(f"提权失败: {e}")
                print("将以当前权限继续运行...")
        else:
            print("用户选择以当前权限运行")
    else:
        print("检测到管理员权限，程序将以最佳模式运行")

    # 运行主程序
    main()
