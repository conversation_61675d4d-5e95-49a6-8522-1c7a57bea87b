#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全检查工具模块
确保程序安全运行，防止误操作
"""

import win32gui
import win32api
import win32con
import pyautogui
import time
from typing import Tuple, Optional, Callable
import threading
from .logger import get_logger

class SafetyChecker:
    """安全检查器"""
    
    def __init__(self):
        self.emergency_stop = False
        self.monitor_thread = None
        self.is_monitoring = False
        self.safety_callback = None
        self.logger = get_logger()

        # 安全设置 - 放宽点击频率限制
        self.max_click_rate = 30  # 每秒最大点击次数（从10提升到30）
        self.click_count = 0
        self.last_reset_time = time.time()

        # 状态管理
        self._stop_event = threading.Event()
        self._cleanup_complete = threading.Event()

        # 键盘状态管理（防止重复触发）
        self._key_states = {
            'esc': False,
            'ctrl_alt_s': False
        }
        self._last_key_check = 0
        self._key_check_interval = 0.3  # 300ms检查间隔，防止过于频繁
        
    def set_safety_callback(self, callback: Callable[[str], None]):
        """设置安全回调函数"""
        self.safety_callback = callback
        
    def _log_safety(self, message: str):
        """记录安全信息"""
        if self.safety_callback:
            self.safety_callback(f"[安全] {message}")
        print(f"[Safety] {message}")
        
    def start_monitoring(self):
        """开始安全监控"""
        if self.is_monitoring:
            self._log_safety("安全监控已在运行中")
            return

        # 重置状态
        self._reset_state()

        self.is_monitoring = True
        self.emergency_stop = False
        self._stop_event.clear()
        self._cleanup_complete.clear()

        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()

        self.logger.log_thread_state("SafetyMonitor", "启动", "安全监控线程已启动")
        self._log_safety("安全监控已启动")
        
    def stop_monitoring(self):
        """停止安全监控"""
        if not self.is_monitoring:
            self._log_safety("安全监控未在运行")
            return

        self._log_safety("正在停止安全监控...")

        # 设置停止信号
        self.is_monitoring = False
        self._stop_event.set()

        # 等待线程结束
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2.0)

            if self.monitor_thread.is_alive():
                self._log_safety("警告：安全监控线程未能正常结束")
                self.logger.log_thread_state("SafetyMonitor", "强制结束", "线程未能在超时时间内结束")
            else:
                self.logger.log_thread_state("SafetyMonitor", "正常结束", "线程已正常结束")

        # 等待清理完成
        if not self._cleanup_complete.wait(timeout=1.0):
            self._log_safety("警告：清理操作未能及时完成")

        self._log_safety("安全监控已停止")
        
    def _monitor_loop(self):
        """监控循环"""
        try:
            self.logger.log_thread_state("SafetyMonitor", "运行中", "开始监控循环")

            while self.is_monitoring and not self._stop_event.is_set():
                try:
                    # 检查紧急停止条件
                    if self._check_emergency_conditions():
                        self.emergency_stop = True
                        self.logger.log_emergency_stop("条件触发", True)
                        self._log_safety("检测到紧急情况，触发安全停止")
                        break

                    # 重置点击计数
                    current_time = time.time()
                    if current_time - self.last_reset_time >= 1.0:
                        self.click_count = 0
                        self.last_reset_time = current_time

                    # 使用事件等待而不是sleep，可以更快响应停止信号
                    # 增加等待时间，减少检查频率，避免过于敏感
                    if self._stop_event.wait(timeout=0.5):
                        break

                except Exception as e:
                    self._log_safety(f"监控过程中发生错误: {e}")
                    self.logger.error_logger.error(f"安全监控错误: {e}")
                    time.sleep(1.0)

        finally:
            # 清理工作
            self._cleanup_monitoring()
            self._cleanup_complete.set()
            self.logger.log_thread_state("SafetyMonitor", "结束", "监控循环已结束")
                
    def _check_emergency_conditions(self) -> bool:
        """检查紧急停止条件（带去抖动）"""
        try:
            current_time = time.time()

            # 限制键盘检查频率，避免过于敏感
            if current_time - self._last_key_check < self._key_check_interval:
                return False

            self._last_key_check = current_time

            # 检查鼠标是否在屏幕左上角（pyautogui的安全机制）
            mouse_x, mouse_y = pyautogui.position()
            if mouse_x == 0 and mouse_y == 0:
                self._log_safety("检测到鼠标左上角紧急停止")
                return True

            # 检查ESC键状态（带去抖动）
            try:
                esc_pressed = bool(win32api.GetAsyncKeyState(win32con.VK_ESCAPE) & 0x8000)

                if esc_pressed and not self._key_states['esc']:
                    # ESC键从未按下变为按下状态 - 触发紧急停止
                    self._key_states['esc'] = True
                    self._log_safety("🔴 检测到ESC键按下，触发紧急停止")
                    self.emergency_stop = True  # 立即设置紧急停止状态
                    return True
                elif not esc_pressed and self._key_states['esc']:
                    # ESC键从按下变为释放状态 - 重置按键状态但保持紧急停止
                    self._key_states['esc'] = False
                    self._log_safety("🟢 ESC键已释放，按键状态重置（紧急停止状态保持）")

            except Exception as e:
                self._log_safety(f"ESC键检测异常: {e}")
                # 发生异常时重置状态
                self._key_states['esc'] = False

            # 检查Ctrl+Alt+S组合键（带去抖动）
            ctrl_alt_s_pressed = bool(
                win32api.GetAsyncKeyState(win32con.VK_CONTROL) & 0x8000 and
                win32api.GetAsyncKeyState(win32con.VK_MENU) & 0x8000 and
                win32api.GetAsyncKeyState(ord('S')) & 0x8000
            )

            if ctrl_alt_s_pressed and not self._key_states['ctrl_alt_s']:
                # 组合键从未按下变为按下状态 - 触发紧急停止
                self._key_states['ctrl_alt_s'] = True
                self._log_safety("检测到Ctrl+Alt+S紧急停止")
                return True
            elif not ctrl_alt_s_pressed:
                # 组合键未按下 - 重置状态（无论之前是什么状态）
                self._key_states['ctrl_alt_s'] = False

        except Exception as e:
            self._log_safety(f"紧急条件检查失败: {e}")

        return False
        
    def is_safe_to_click(self, x: int, y: int, target_region: Tuple[int, int, int, int],
                        target_hwnd: Optional[int] = None) -> bool:
        """检查是否安全点击（简化版）"""
        try:
            # 检查紧急停止状态
            if self.emergency_stop:
                return False

            # 检查点击频率（放宽限制）
            if not self._check_click_rate():
                # 减少频率限制日志的输出频率
                if not hasattr(self, '_rate_limit_count'):
                    self._rate_limit_count = 0
                self._rate_limit_count += 1

                if self._rate_limit_count <= 3 or self._rate_limit_count % 20 == 0:
                    self._log_safety(f"点击频率限制 (第{self._rate_limit_count}次)")
                return False
            else:
                # 重置频率限制计数
                if hasattr(self, '_rate_limit_count'):
                    self._rate_limit_count = 0

            # 简化位置检查 - 只检查基本边界
            if target_region:
                left, top, right, bottom = target_region
                if not (left <= x <= right and top <= y <= bottom):
                    self._log_safety(f"点击位置 ({x}, {y}) 超出目标区域 {target_region}")
                    return False

            # 简化窗口检查 - 只检查窗口是否存在
            if target_hwnd and not self._is_window_valid(target_hwnd):
                self._log_safety("目标窗口已无效")
                return False

            return True

        except Exception as e:
            self._log_safety(f"安全检查失败: {e}")
            return False
            
    def _check_click_rate(self) -> bool:
        """检查点击频率"""
        self.click_count += 1
        return self.click_count <= self.max_click_rate
        
    def _is_position_in_region(self, x: int, y: int, region: Tuple[int, int, int, int]) -> bool:
        """检查位置是否在区域内"""
        left, top, right, bottom = region
        return left <= x <= right and top <= y <= bottom
        
    def _is_window_valid(self, hwnd: int) -> bool:
        """检查窗口是否有效"""
        try:
            return win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd)
        except:
            return False
            
    def _is_window_accessible(self, hwnd: int) -> bool:
        """检查窗口是否可访问"""
        try:
            # 检查窗口是否最小化
            if win32gui.IsIconic(hwnd):
                return False
                
            # 获取窗口矩形
            rect = win32gui.GetWindowRect(hwnd)
            if rect[2] - rect[0] <= 0 or rect[3] - rect[1] <= 0:
                return False
                
            return True
        except:
            return False
            
    def get_safety_status(self) -> dict:
        """获取安全状态"""
        return {
            'is_monitoring': self.is_monitoring,
            'emergency_stop': self.emergency_stop,
            'click_count': self.click_count,
            'max_click_rate': self.max_click_rate
        }
        
    def reset_emergency_stop(self):
        """重置紧急停止状态"""
        old_state = self.emergency_stop
        self.emergency_stop = False

        # 重置键盘状态，防止重复触发
        self._key_states = {
            'esc': False,
            'ctrl_alt_s': False
        }
        self._last_key_check = 0

        if old_state:
            self.logger.log_emergency_stop("手动重置", True)
            self._log_safety("紧急停止状态已重置")
        else:
            self._log_safety("紧急停止状态无需重置")

    def _reset_state(self):
        """重置内部状态"""
        self.click_count = 0
        self.last_reset_time = time.time()
        self.emergency_stop = False

        # 重置失败计数器
        if hasattr(self, '_win32_fail_count'):
            delattr(self, '_win32_fail_count')

        self._log_safety("内部状态已重置")

    def _cleanup_monitoring(self):
        """清理监控资源"""
        try:
            # 这里可以添加任何需要清理的资源
            # 例如：关闭文件句柄、释放系统资源等
            self._log_safety("监控资源清理完成")
        except Exception as e:
            self._log_safety(f"监控资源清理失败: {e}")

    def force_reset(self):
        """强制重置所有状态（用于紧急恢复）"""
        self._log_safety("执行强制重置...")

        # 停止监控
        if self.is_monitoring:
            self.stop_monitoring()

        # 重置所有状态
        self._reset_state()

        # 清理线程引用
        self.monitor_thread = None

        self.logger.log_thread_state("SafetyMonitor", "强制重置", "所有状态已强制重置")
        self._log_safety("强制重置完成，可以重新启动")
        
    def set_max_click_rate(self, rate: int):
        """设置最大点击频率"""
        self.max_click_rate = max(1, min(rate, 20))  # 限制在1-20之间
        self._log_safety(f"最大点击频率设置为: {self.max_click_rate}/秒")
