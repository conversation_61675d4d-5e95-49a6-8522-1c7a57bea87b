#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏窗口管理器
专门处理梦幻西游窗口焦点、坐标转换和点击优化
"""

import win32gui
import win32con
import win32api
import time
from typing import Tuple, Optional, Dict, Any
from .logger import get_logger

class GameWindowManager:
    """游戏窗口管理器"""
    
    def __init__(self):
        self.logger = get_logger()
        self.target_hwnd = None
        self.window_rect = None
        self.client_rect = None
        self.last_focus_time = 0
        
    def set_target_window(self, hwnd: int) -> bool:
        """设置目标游戏窗口"""
        try:
            if not win32gui.IsWindow(hwnd):
                self.logger.error_logger.error(f"无效的窗口句柄: {hwnd}")
                return False
                
            self.target_hwnd = hwnd
            self._update_window_info()
            
            window_title = win32gui.GetWindowText(hwnd)
            self.logger.main_logger.info(f"设置目标窗口: {window_title} (句柄: {hwnd})")
            
            return True
            
        except Exception as e:
            self.logger.error_logger.error(f"设置目标窗口失败: {e}")
            return False
            
    def _update_window_info(self):
        """更新窗口信息"""
        try:
            if not self.target_hwnd:
                return
                
            # 获取窗口矩形
            self.window_rect = win32gui.GetWindowRect(self.target_hwnd)
            
            # 获取客户区矩形
            client_rect = win32gui.GetClientRect(self.target_hwnd)
            client_point = win32gui.ClientToScreen(self.target_hwnd, (0, 0))
            self.client_rect = (
                client_point[0],
                client_point[1],
                client_point[0] + client_rect[2],
                client_point[1] + client_rect[3]
            )
            
            self.logger.main_logger.info(f"窗口信息更新 - 窗口矩形: {self.window_rect}, 客户区: {self.client_rect}")
            
        except Exception as e:
            self.logger.error_logger.error(f"更新窗口信息失败: {e}")
            
    def ensure_window_focus(self) -> bool:
        """确保游戏窗口获得焦点"""
        try:
            if not self.target_hwnd:
                return False
                
            # 检查窗口是否仍然有效
            if not win32gui.IsWindow(self.target_hwnd):
                self.logger.error_logger.error("目标窗口已不存在")
                return False
                
            # 检查窗口是否最小化
            if win32gui.IsIconic(self.target_hwnd):
                # 恢复窗口
                win32gui.ShowWindow(self.target_hwnd, win32con.SW_RESTORE)
                time.sleep(0.1)
                
            # 将窗口置于前台
            current_foreground = win32gui.GetForegroundWindow()
            if current_foreground != self.target_hwnd:
                # 尝试设置前台窗口
                win32gui.SetForegroundWindow(self.target_hwnd)
                time.sleep(0.1)
                
                # 验证是否成功
                new_foreground = win32gui.GetForegroundWindow()
                if new_foreground == self.target_hwnd:
                    self.last_focus_time = time.time()
                    self.logger.main_logger.info("游戏窗口已获得焦点")
                    return True
                else:
                    self.logger.error_logger.warning("无法将游戏窗口置于前台")
                    return False
            else:
                self.last_focus_time = time.time()
                return True
                
        except Exception as e:
            self.logger.error_logger.error(f"设置窗口焦点失败: {e}")
            return False
            
    def is_coordinate_in_game_area(self, x: int, y: int, user_region: tuple = None) -> bool:
        """检查坐标是否在游戏可操作区域内（优先使用用户选择区域）"""
        try:
            # 如果有用户选择的区域，优先使用用户区域进行检查
            if user_region:
                left, top, right, bottom = user_region
                # 只检查是否在用户选择区域内，不做UI排除
                in_user_area = left <= x <= right and top <= y <= bottom
                if in_user_area:
                    return True
                else:
                    self.logger.main_logger.debug(f"坐标 ({x}, {y}) 不在用户选择区域 {user_region} 内")
                    return False

            # 回退到自动检测模式
            if not self.client_rect:
                self._update_window_info()

            if not self.client_rect:
                return False

            left, top, right, bottom = self.client_rect

            # 简化UI排除区域，只排除最关键的部分
            ui_exclusions = [
                # 只排除顶部菜单栏 (减少到30像素)
                (left, top, right, top + 30),
                # 只排除底部聊天栏 (减少到60像素)
                (left, bottom - 60, right, bottom),
            ]

            # 检查是否在客户区内
            if not (left <= x <= right and top <= y <= bottom):
                return False

            # 检查是否在UI排除区域内
            for ex_left, ex_top, ex_right, ex_bottom in ui_exclusions:
                if ex_left <= x <= ex_right and ex_top <= y <= ex_bottom:
                    self.logger.main_logger.debug(f"坐标 ({x}, {y}) 在UI区域内，跳过")
                    return False

            return True

        except Exception as e:
            self.logger.error_logger.error(f"检查坐标区域失败: {e}")
            return False
            
    def get_safe_click_area(self, user_region: Optional[Tuple[int, int, int, int]] = None) -> Optional[Tuple[int, int, int, int]]:
        """获取安全的点击区域（优先使用用户选择区域）"""
        try:
            # 如果用户指定了区域，优先使用用户区域
            if user_region:
                left, top, right, bottom = user_region

                # 只做最小的边距调整，保持用户选择的大部分区域
                margin = 5  # 减少边距到5像素
                safe_left = left + margin
                safe_top = top + margin
                safe_right = right - margin
                safe_bottom = bottom - margin

                # 确保区域有效
                if safe_left >= safe_right or safe_top >= safe_bottom:
                    self.logger.error_logger.warning("用户区域过小，使用原始区域")
                    return user_region

                safe_area = (safe_left, safe_top, safe_right, safe_bottom)
                # 减少日志频率，只在区域变化时记录
                if not hasattr(self, '_last_safe_area') or self._last_safe_area != safe_area:
                    self.logger.main_logger.info(f"使用用户指定区域: {safe_area}")
                    self._last_safe_area = safe_area

                return safe_area

            # 回退到自动计算的安全区域
            if not self.client_rect:
                self._update_window_info()

            if not self.client_rect:
                return None

            left, top, right, bottom = self.client_rect

            # 计算游戏主要可操作区域（减少UI排除范围）
            safe_left = left + 100    # 减少左侧UI排除范围
            safe_top = top + 30       # 减少顶部UI排除范围
            safe_right = right - 100  # 减少右侧UI排除范围
            safe_bottom = bottom - 80 # 减少底部UI排除范围

            # 确保区域有效
            if safe_left >= safe_right or safe_top >= safe_bottom:
                self.logger.error_logger.warning("计算的安全区域无效，使用整个客户区")
                return self.client_rect

            safe_area = (safe_left, safe_top, safe_right, safe_bottom)
            # 减少日志频率
            if not hasattr(self, '_last_auto_area') or self._last_auto_area != safe_area:
                self.logger.main_logger.info(f"自动计算安全区域: {safe_area}")
                self._last_auto_area = safe_area

            return safe_area

        except Exception as e:
            self.logger.error_logger.error(f"获取安全点击区域失败: {e}")
            return None
            
    def optimize_click_coordinate(self, x: int, y: int) -> Tuple[int, int]:
        """优化点击坐标，确保在安全区域内"""
        try:
            safe_area = self.get_safe_click_area()
            if not safe_area:
                return x, y
                
            safe_left, safe_top, safe_right, safe_bottom = safe_area
            
            # 将坐标限制在安全区域内
            optimized_x = max(safe_left, min(x, safe_right))
            optimized_y = max(safe_top, min(y, safe_bottom))
            
            if (optimized_x, optimized_y) != (x, y):
                self.logger.main_logger.info(f"坐标优化: ({x}, {y}) -> ({optimized_x}, {optimized_y})")
                
            return optimized_x, optimized_y
            
        except Exception as e:
            self.logger.error_logger.error(f"优化点击坐标失败: {e}")
            return x, y
            
    def prepare_for_click(self) -> bool:
        """为点击操作做准备"""
        try:
            # 更新窗口信息
            self._update_window_info()
            
            # 确保窗口焦点
            if not self.ensure_window_focus():
                return False
                
            # 短暂等待确保窗口响应
            time.sleep(0.05)
            
            return True
            
        except Exception as e:
            self.logger.error_logger.error(f"点击准备失败: {e}")
            return False
            
    def get_window_status(self) -> Dict[str, Any]:
        """获取窗口状态信息"""
        try:
            if not self.target_hwnd:
                return {"status": "未设置目标窗口"}
                
            is_valid = win32gui.IsWindow(self.target_hwnd)
            is_visible = win32gui.IsWindowVisible(self.target_hwnd) if is_valid else False
            is_foreground = win32gui.GetForegroundWindow() == self.target_hwnd if is_valid else False
            is_minimized = win32gui.IsIconic(self.target_hwnd) if is_valid else False
            
            window_title = ""
            if is_valid:
                try:
                    window_title = win32gui.GetWindowText(self.target_hwnd)
                except:
                    window_title = "无法获取标题"
                    
            return {
                "hwnd": self.target_hwnd,
                "title": window_title,
                "is_valid": is_valid,
                "is_visible": is_visible,
                "is_foreground": is_foreground,
                "is_minimized": is_minimized,
                "window_rect": self.window_rect,
                "client_rect": self.client_rect,
                "last_focus_time": self.last_focus_time
            }
            
        except Exception as e:
            self.logger.error_logger.error(f"获取窗口状态失败: {e}")
            return {"status": "获取状态失败", "error": str(e)}
