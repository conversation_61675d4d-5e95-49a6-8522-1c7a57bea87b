#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证码检测器
基于图像识别检测梦幻西游验证码弹窗
"""

import os
import time
import math
import threading
import numpy as np
import cv2
from ctypes import windll, byref, c_ubyte
from ctypes.wintypes import RECT, HWND
from typing import Tuple, Optional, Callable
from .logger import get_logger

class CaptchaDetector:
    """验证码检测器"""
    
    def __init__(self):
        self.logger = get_logger()
        self.is_monitoring = False
        self.monitor_thread = None
        self.target_hwnd = None
        self.captcha_template_path = None
        
        # 检测参数
        self.detection_interval = 2.0  # 检测间隔（秒）
        self.match_threshold = 0.8     # 匹配阈值
        self.notification_interval = 300  # 通知间隔（秒）
        
        # 回调函数
        self.captcha_detected_callback = None
        self.status_callback = None
        
        # 状态管理
        self._stop_event = threading.Event()
        self.last_detection_time = 0

        # 调试选项
        self._debug_mode = False
        self._save_debug_images = False

        # 初始化Win32 API
        self._init_win32_api()
        
    def _init_win32_api(self):
        """初始化Win32 API"""
        try:
            # 防止UI放大导致截图不完整
            windll.user32.SetProcessDPIAware()
            
            # 获取API函数
            self.GetDC = windll.user32.GetDC
            self.CreateCompatibleDC = windll.gdi32.CreateCompatibleDC
            self.GetClientRect = windll.user32.GetClientRect
            self.CreateCompatibleBitmap = windll.gdi32.CreateCompatibleBitmap
            self.SelectObject = windll.gdi32.SelectObject
            self.BitBlt = windll.gdi32.BitBlt
            self.GetBitmapBits = windll.gdi32.GetBitmapBits
            self.DeleteObject = windll.gdi32.DeleteObject
            self.ReleaseDC = windll.user32.ReleaseDC
            
            self.SRCCOPY = 0x00CC0020
            
            self._log_status("Win32 API初始化成功")
            
        except Exception as e:
            self._log_status(f"Win32 API初始化失败: {e}")
            
    def set_target_window(self, hwnd: int):
        """设置目标窗口句柄"""
        self.target_hwnd = hwnd
        self._log_status(f"设置目标窗口: {hwnd}")
        
    def set_captcha_template(self, template_path: str):
        """设置验证码模板图片路径"""
        if os.path.exists(template_path):
            self.captcha_template_path = template_path
            self._log_status(f"设置验证码模板: {template_path}")
        else:
            self._log_status(f"验证码模板文件不存在: {template_path}")
            
    def set_captcha_detected_callback(self, callback: Callable[[], None]):
        """设置验证码检测到时的回调函数"""
        self.captcha_detected_callback = callback
        
    def set_status_callback(self, callback: Callable[[str], None]):
        """设置状态回调函数"""
        self.status_callback = callback
        
    def _log_status(self, message: str):
        """记录状态信息"""
        if self.status_callback:
            self.status_callback(f"[验证码检测] {message}")
        self.logger.main_logger.info(f"[验证码检测] {message}")

    def _save_debug_image(self, image: np.ndarray, template: np.ndarray, match_loc: tuple, match_val: float):
        """保存调试图像"""
        try:
            import os
            debug_dir = "debug_images"
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)

            timestamp = time.strftime("%Y%m%d_%H%M%S")

            # 保存原始截图
            cv2.imwrite(f"{debug_dir}/screenshot_{timestamp}.png", image)

            # 保存模板
            cv2.imwrite(f"{debug_dir}/template_{timestamp}.png", template)

            # 在截图上标记匹配位置
            h, w = template.shape[:2]
            top_left = match_loc
            bottom_right = (top_left[0] + w, top_left[1] + h)

            marked_image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
            cv2.rectangle(marked_image, top_left, bottom_right, (0, 255, 0), 2)
            cv2.putText(marked_image, f"Match: {match_val:.3f}",
                       (top_left[0], top_left[1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            cv2.imwrite(f"{debug_dir}/marked_{timestamp}.png", marked_image)

            self._log_status(f"🖼️ 调试图像已保存到 {debug_dir}/")

        except Exception as e:
            self._log_status(f"保存调试图像失败: {e}")

    def enable_debug_mode(self, enabled: bool = True, save_images: bool = False):
        """启用调试模式"""
        self._debug_mode = enabled
        self._save_debug_images = save_images
        if enabled:
            self._log_status(f"🔧 调试模式已启用，保存图像: {save_images}")
        else:
            self._log_status("🔧 调试模式已禁用")
        
    def _capture_window(self, hwnd: HWND) -> Optional[np.ndarray]:
        """截取窗口客户区"""
        try:
            # 获取窗口客户区大小
            rect = RECT()
            self.GetClientRect(hwnd, byref(rect))
            width, height = rect.right, rect.bottom
            
            if width <= 0 or height <= 0:
                return None
                
            # 开始截图
            dc = self.GetDC(hwnd)
            cdc = self.CreateCompatibleDC(dc)
            bitmap = self.CreateCompatibleBitmap(dc, width, height)
            self.SelectObject(cdc, bitmap)
            self.BitBlt(cdc, 0, 0, width, height, dc, 0, 0, self.SRCCOPY)
            
            # 获取位图数据
            total_bytes = width * height * 4
            buffer = bytearray(total_bytes)
            byte_array = c_ubyte * total_bytes
            self.GetBitmapBits(bitmap, total_bytes, byte_array.from_buffer(buffer))
            
            # 清理资源
            self.DeleteObject(bitmap)
            self.DeleteObject(cdc)
            self.ReleaseDC(hwnd, dc)
            
            # 返回numpy数组
            return np.frombuffer(buffer, dtype=np.uint8).reshape(height, width, 4)
            
        except Exception as e:
            self._log_status(f"窗口截图失败: {e}")
            return None
            
    def _detect_captcha_in_image(self, image: np.ndarray) -> Tuple[bool, int, int]:
        """在图像中检测验证码"""
        try:
            if self.captcha_template_path is None:
                self._log_status("⚠️ 验证码模板路径为空")
                return False, 0, 0

            # 读取验证码模板
            template = cv2.imread(self.captcha_template_path, cv2.IMREAD_UNCHANGED)
            if template is None:
                self._log_status(f"❌ 无法读取验证码模板: {self.captcha_template_path}")
                return False, 0, 0

            # 转换为灰度图
            if len(template.shape) == 3:
                template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            else:
                template_gray = template

            image_gray = cv2.cvtColor(image, cv2.COLOR_BGRA2GRAY)

            # 记录图像信息
            self._log_status(f"🔍 图像尺寸: {image_gray.shape}, 模板尺寸: {template_gray.shape}")

            # 模板匹配
            result = cv2.matchTemplate(image_gray, template_gray, cv2.TM_CCOEFF_NORMED)

            # 获取最佳匹配位置
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

            # 详细记录匹配结果
            self._log_status(f"🎯 匹配结果: 最大值={max_val:.3f}, 阈值={self.match_threshold}, 位置={max_loc}")

            if max_val >= self.match_threshold:
                # 计算中心点坐标
                h, w = template_gray.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2

                self._log_status(f"🚨 检测到验证码！匹配度: {max_val:.3f}, 位置: ({center_x}, {center_y})")

                # 保存调试图像（可选）
                if hasattr(self, '_save_debug_images') and self._save_debug_images:
                    self._save_debug_image(image_gray, template_gray, max_loc, max_val)

                return True, center_x, center_y
            else:
                # 记录未匹配的原因
                if max_val < self.match_threshold * 0.5:
                    self._log_status(f"🔍 匹配度过低: {max_val:.3f} < {self.match_threshold} (可能模板不匹配)")
                else:
                    self._log_status(f"🔍 匹配度接近但未达到阈值: {max_val:.3f} < {self.match_threshold}")
                return False, 0, 0

        except Exception as e:
            self._log_status(f"💥 验证码检测异常: {e}")
            import traceback
            self._log_status(f"详细错误: {traceback.format_exc()}")
            return False, 0, 0
            
    def _should_notify(self) -> bool:
        """检查是否应该发送通知（防止重复通知）"""
        current_time = time.time()
        if current_time - self.last_detection_time > self.notification_interval:
            self.last_detection_time = current_time
            return True
        return False
        
    def detect_captcha(self) -> Tuple[bool, int, int]:
        """检测验证码（单次检测）"""
        if not self.target_hwnd:
            self._log_status("⚠️ 未设置目标窗口，跳过检测")
            return False, 0, 0

        if not self.captcha_template_path:
            self._log_status("⚠️ 未设置验证码模板，跳过检测")
            return False, 0, 0

        # 截取窗口
        image = self._capture_window(self.target_hwnd)
        if image is None:
            self._log_status("⚠️ 窗口截图失败，跳过检测")
            return False, 0, 0

        # 检测验证码
        detected, x, y = self._detect_captcha_in_image(image)

        # 记录检测结果（详细模式）
        if hasattr(self, '_debug_mode') and self._debug_mode:
            if detected:
                self._log_status(f"🎯 验证码检测成功: 位置({x}, {y})")
            else:
                self._log_status(f"🔍 验证码检测: 未发现目标")

        return detected, x, y
        
    def _monitor_loop(self):
        """监控循环"""
        self._log_status("🔍 验证码监控开始")
        detection_count = 0

        while not self._stop_event.is_set():
            try:
                detection_count += 1
                start_time = time.time()

                # 检测验证码
                detected, x, y = self.detect_captcha()
                detection_time = time.time() - start_time

                # 记录检测日志（每10次记录一次正常检测）
                if detection_count % 10 == 0 or detected:
                    if detected:
                        self._log_status(f"🚨 第{detection_count}次检测: 发现验证码! 位置:({x},{y}) 耗时:{detection_time:.3f}s")
                    else:
                        self._log_status(f"✅ 第{detection_count}次检测: 无验证码 耗时:{detection_time:.3f}s")

                if detected and self._should_notify():
                    self._log_status("🚨 验证码检测触发通知！")

                    # 记录到日志
                    self.logger.log_emergency_stop("验证码检测", True)

                    # 调用回调函数
                    if self.captcha_detected_callback:
                        try:
                            self.captcha_detected_callback()
                        except Exception as e:
                            self._log_status(f"验证码回调执行失败: {e}")

                # 等待下次检测
                if self._stop_event.wait(self.detection_interval):
                    break

            except Exception as e:
                self._log_status(f"💥 监控循环异常: {e}")
                time.sleep(1)

        self._log_status(f"🔍 验证码监控结束，共检测{detection_count}次")
        
    def start_monitoring(self) -> bool:
        """开始监控"""
        if self.is_monitoring:
            self._log_status("验证码监控已在运行")
            return False
            
        if not self.target_hwnd:
            self._log_status("未设置目标窗口，无法开始监控")
            return False
            
        if not self.captcha_template_path:
            self._log_status("未设置验证码模板，无法开始监控")
            return False
            
        try:
            self._stop_event.clear()
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            self.is_monitoring = True
            
            self._log_status("验证码监控已启动")
            return True
            
        except Exception as e:
            self._log_status(f"启动验证码监控失败: {e}")
            return False
            
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
            
        self._stop_event.set()
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
            
        self.is_monitoring = False
        self._log_status("验证码监控已停止")
        
    def get_status(self) -> dict:
        """获取检测器状态"""
        return {
            "is_monitoring": self.is_monitoring,
            "target_hwnd": self.target_hwnd,
            "template_path": self.captcha_template_path,
            "detection_interval": self.detection_interval,
            "match_threshold": self.match_threshold,
            "last_detection_time": self.last_detection_time
        }
